FROM node:20-alpine

# Create app directory
RUN mkdir /app
COPY . /app/
WORKDIR /app

RUN yarn install --silent
RUN yarn build

# # Compile server.ts to CommonJS and rename to .cjs
# RUN npx tsc server.ts --target ES2020 --module commonjs --esModuleInterop --outDir .
# RUN mv server.js server.cjs

# CMD [ "node", "server.cjs" ]
# Install tsx globally
RUN npm install -g tsx

# Run TypeScript directly with tsx
CMD ["tsx", "server.ts"]

# Expose PORT 3000 on our virtual machine so we canyar run our server
EXPOSE 3000
