import { useAppDispatch } from '@/redux/store'
import { AsyncThunk } from '@reduxjs/toolkit'
import { useCallback, useEffect, useRef, useState } from 'react'

export function useAsyncThunk<Returned, ThunkArg = void, Formatted = Returned>(
  asyncThunk: AsyncThunk<Returned, ThunkArg, { rejectValue: string }>,
  options?: {
    immediate?: boolean
    initialArg?: ThunkArg extends void ? void : ThunkArg
    formatter?: (data: Returned) => Formatted
  }
) {
  const dispatch = useAppDispatch()
  const [data, setData] = useState<Formatted | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const optionsRef = useRef(options)

  // Update the ref when options change
  useEffect(() => {
    optionsRef.current = options
  }, [options])

  type ResolvedArg = ThunkArg extends void ? void : ThunkArg

  const execute = useCallback(
    async (arg?: ResolvedArg): Promise<Formatted | null> => {
      setIsLoading(true)
      setError(null)

      try {
        const resultAction = await dispatch(asyncThunk(arg as any))
        if (asyncThunk.fulfilled.match(resultAction)) {
          const payload = resultAction.payload as Returned
          const formatted = optionsRef.current?.formatter
            ? optionsRef.current?.formatter(payload)
            : (payload as unknown as Formatted)
          setData(formatted)
          return formatted
        }

        if (asyncThunk.rejected.match(resultAction)) {
          const errorMessage =
            (resultAction.payload as string) ||
            (resultAction.error as { message?: string })?.message ||
            'Unknown error'
          setError(errorMessage)
          setData(null)
          return null
        }

        // Fallback
        setError('Unexpected response format')
        setData(null)
        return null
      } catch (err) {
        setError('An unexpected error occurred')
        setData(null)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [dispatch, asyncThunk]
  )

  useEffect(() => {
    const immediate = optionsRef.current?.immediate ?? true
    const initialArg = optionsRef.current?.initialArg

    if (immediate) {
      execute(initialArg as ResolvedArg)
    }
  }, [execute])

  return {
    execute,
    data,
    isLoading,
    error,
  }
}
