import { useNavigate, useLocation } from 'react-router-dom'
import {
  createQueryParams,
  getFilterChipsAndParams,
  useQuery,
} from '@/utils/Routerutils'

export function useSearchParams(filterKeySet: any) {
  const params = useQuery()
  const navigate = useNavigate()
  const location = useLocation()
  const filterState: any = getFilterChipsAndParams(params, filterKeySet)
  function addFiltersQueryParams(filterChips: any, filterParams: any) {
    const appliedFilters = createQueryParams(
      params,
      filterChips,
      filterParams,
      filterKeySet
    )
    navigate(
      {
        pathname: location.pathname,
        search: appliedFilters.toString(),
      },
      { replace: true }
    )
  }

  function removeFiltersQueryParams(filterKeys: Array<any>) {
    // eslint-disable-next-line
    filterKeys &&
      filterKeys.map((element: any) => {
        params.delete(element)
        params.delete(filterKeySet[element])
      })
    navigate(
      {
        pathname: location.pathname,
        search: params.toString(),
      },
      { replace: true }
    )
  }

  return [filterState, addFiltersQueryParams, removeFiltersQueryParams]
}
