import ListingFilterActionTypes from '@/pages/loads/listing/reducers/listingFilter/ListingFilterActionTypes'
import { LoadListingFilterOptionLists } from '@/pages/loads/listing/types/ListingFilterModel'
import { loadListingFilters } from '@/utils/FilterUtils'
import { OptionType } from '@/components/widgets/widgetsInterfaces'

export const setFilterValuesAndParams = (
  filterValues: Partial<Record<keyof typeof loadListingFilters, string>>,
  filterParams: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >,
  isFilterChanged: boolean = true
) => ({
  type: ListingFilterActionTypes.SET_FILTER_VALUES_AND_PARAMS,
  filterValues,
  filterParams,
  isFilterChanged,
})

export const setAutoCompleteOptionsList = (
  optionType: keyof LoadListingFilterOptionLists,
  value: OptionType[]
) => ({
  type: ListingFilterActionTypes.SET_OPTION_LISTS,
  optionType,
  value,
})

export const setIsLoadingFilterOptions = (
  loadingOptionsTypes: (keyof LoadListingFilterOptionLists)[],
  isLoading: boolean
) => ({
  type: ListingFilterActionTypes.IS_LOADING_OPTIONS,
  loadingOptionsTypes,
  isLoading,
})

export const resetFilters = () => ({
  type: ListingFilterActionTypes.RESET_FILTERS,
})

export const setError = (
  errors: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >
) => ({
  type: ListingFilterActionTypes.SET_ERROR,
  errors,
})
