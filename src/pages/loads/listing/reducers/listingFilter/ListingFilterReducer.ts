import { createReducer } from 'reduxsauce'
import ListingFilterActionTypes from '@/pages/loads/listing/reducers/listingFilter/ListingFilterActionTypes'
import {
  LoadListingFilterOptionLists,
  LoadListingFilterState,
} from '@/pages/loads/listing/types/ListingFilterModel'
import { loadListingFilters } from '@/utils/FilterUtils'
import { OptionType } from '@/components/widgets/widgetsInterfaces'

export const LISTING_FILTER_STATE: LoadListingFilterState = {
  filterParams: {},
  filterValues: {},
  optionLists: {
    customers: [],
    origins: [],
    destinations: [],
    vehicleTypes: [],
    vehicleNumbers: [],
    loadTypes: [],
    indentTypes: [],
  },
  isLoadingOptions: {
    customers: false,
    origins: false,
    destinations: false,
    vehicleTypes: false,
    vehicleNumbers: false,
    loadTypes: false,
    indentTypes: false,
  },
  isFilterChanged: false,
  error: {},
  selectedCustomerId: undefined,
}

const setFilterValuesAndParamsReducer = (
  state = LISTING_FILTER_STATE,
  action: {
    type: string
    filterValues: Partial<Record<keyof typeof loadListingFilters, string>>
    filterParams: Partial<
      Record<
        (typeof loadListingFilters)[keyof typeof loadListingFilters],
        string
      >
    >
    isFilterChanged: boolean
  }
) => {
  return {
    ...state,
    filterValues: {
      ...state.filterValues,
      ...action.filterValues,
    },
    filterParams: {
      ...state.filterParams,
      ...action.filterParams,
    },
    isFilterChanged: action.isFilterChanged,
    error: {},
  }
}

const setOptionListsReducer = (
  state = LISTING_FILTER_STATE,
  action: {
    type: string
    optionType: keyof LoadListingFilterOptionLists
    value: OptionType[]
  }
) => ({
  ...state,
  optionLists: {
    ...state.optionLists,
    [action.optionType]: action.value,
  },
})

const setErrorsReducer = (
  state = LISTING_FILTER_STATE,
  action: {
    type: string
    errors: Partial<
      Record<
        (typeof loadListingFilters)[keyof typeof loadListingFilters],
        string
      >
    >
  }
) => ({
  ...state,
  error: action.errors,
})

const setIsLoadingOptionsReducer = (
  state = LISTING_FILTER_STATE,
  action: {
    type: string
    loadingOptionsTypes: (keyof LoadListingFilterOptionLists)[]
    isLoading: boolean
  }
) => {
  const updatedLoadingState = { ...state.isLoadingOptions }

  action.loadingOptionsTypes.forEach((key) => {
    updatedLoadingState[key] = action.isLoading
  })

  return {
    ...state,
    isLoadingOptions: updatedLoadingState,
  }
}

const resetFiltersReducer = (state = LISTING_FILTER_STATE) => ({
  ...state,
  filterParams: {},
  filterValues: {},
  isFilterChanged: false,
  error: {},
})

const ACTION_HANDLERS = {
  [ListingFilterActionTypes.SET_FILTER_VALUES_AND_PARAMS]:
    setFilterValuesAndParamsReducer,
  [ListingFilterActionTypes.SET_OPTION_LISTS]: setOptionListsReducer,
  [ListingFilterActionTypes.IS_LOADING_OPTIONS]: setIsLoadingOptionsReducer,
  [ListingFilterActionTypes.SET_ERROR]: setErrorsReducer,
  [ListingFilterActionTypes.RESET_FILTERS]: resetFiltersReducer,
}

const ListingFilterReducer = createReducer(
  LISTING_FILTER_STATE,
  ACTION_HANDLERS
)

export default ListingFilterReducer
