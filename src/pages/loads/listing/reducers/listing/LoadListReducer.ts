import { LoadListingModuleState } from '@/pages/loads/listing/LoadListingModels'
import {
  loadsListingStatusArray,
  rowsPerPageOptions,
} from '@/constant/ArrayList'
import { isMobile } from '@/utils/ViewUtils'
import { createReducer } from 'reduxsauce'
import LoadListModuleActionTypes from '@/pages/loads/listing/reducers/listing/LoadListModuleActionTypes'
import { LoadListDetails } from '@/api/types'

export const LOAD_LISTING_MODULE_STATE: LoadListingModuleState = {
  openFilter: false,
  pagination: undefined,
  listData: [],
  selectedTab: {
    index: 0,
    name: loadsListingStatusArray[0],
  },
  currentPage: 1,
  refreshList: false,
  loading: false,
  pageSize: rowsPerPageOptions[0],
  filterParams: {},
  filterChips: {},
  selectedRowIndex: undefined,
}

const toggleFilterReducer = (state = LOAD_LISTING_MODULE_STATE) => ({
  ...state,
  openFilter: !state.openFilter,
})

const setResponseReducer = (
  state = LOAD_LISTING_MODULE_STATE,
  action: {
    type: string
    response: LoadListDetails
    selectedTabIndex: number
    isHomePage: boolean
  }
) => {
  let newState = {
    ...state,
    pagination: action.response && action.response.pagination,
    selectedTab: {
      index: action.selectedTabIndex,
      name: loadsListingStatusArray[action.selectedTabIndex],
    },
    selectedRowIndex: undefined,
  }
  if (isMobile) {
    if (action.isHomePage || state.currentPage === newState.currentPage) {
      newState = {
        ...newState,
        listData: action.response && action.response.data,
      }
    } else {
      newState = {
        ...newState,
        listData: newState.listData
          ? [...newState.listData, ...(action.response && action.response.data)]
          : action.response && action.response.data,
      }
    }
  } else {
    newState = {
      ...newState,
      listData: action.response && action.response.data,
    }
  }
  return newState
}

const setCurrentPageReducer = (
  state = LOAD_LISTING_MODULE_STATE,
  action: {
    type: string
    pageNumber: number
  }
) => ({
  ...state,
  currentPage: action.pageNumber,
})

const refreshListReducer = (state = LOAD_LISTING_MODULE_STATE) => {
  const newState = {
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: [],
  }
  return newState
}

const setRowPerPageReducer = (
  state = LOAD_LISTING_MODULE_STATE,
  action: {
    type: string
    rowsPerPage: number
  }
) => ({
  ...state,
  pageSize: action.rowsPerPage,
  currentPage: 1,
  listData: [],
})

const showLoadingReducer = (state = LOAD_LISTING_MODULE_STATE) => {
  const newState = {
    ...state,
    loading: true,
  }
  return newState
}

const hideLoadingReducer = (state = LOAD_LISTING_MODULE_STATE) => ({
  ...state,
  loading: false,
})

const setSelectedTabReducer = (
  state = LOAD_LISTING_MODULE_STATE,
  action: {
    type: string
    tabIndex: number
  }
) => {
  const newState = {
    ...state,
    currentPage: 1,
    pageSize: rowsPerPageOptions[0],
    selectedTab: {
      index: action.tabIndex,
      name: loadsListingStatusArray[action.tabIndex],
    },
    listData: [],
    selectedRowIndex: undefined,
  }
  return newState
}

const setValueReducer = (
  state = LOAD_LISTING_MODULE_STATE,
  action: {
    type: string
    value: unknown
    valueType: keyof LoadListingModuleState
  }
) => ({
  ...state,
  [action.valueType]: action.value,
})

const setSelectedRowReducer = (
  state = LOAD_LISTING_MODULE_STATE,
  action: {
    type: string
    selectedRowIndex: number
  }
) => ({
  ...state,
  listData: state.listData.map((item: any, index: number) => ({
    ...item,
    selectedRowIndex: index === action.selectedRowIndex ? index : undefined,
  })),
  selectedRowIndex: action.selectedRowIndex,
})

const unsetSelectedRowReducer = (state = LOAD_LISTING_MODULE_STATE) => ({
  ...state,
  listData: state.listData.map((item: any) => ({
    ...item,
    selectedRowIndex: undefined,
  })),
  selectedRowIndex: undefined,
})

const applyFilterReducer = (state = LOAD_LISTING_MODULE_STATE) => ({
  ...state,
  currentPage: 1,
  listData: [],
  openFilter: !state.openFilter,
  refreshList: !state.refreshList,
  selectedRowIndex: undefined,
})

const ACTION_HANDLERS = {
  [LoadListModuleActionTypes.TOGGLE_FILTER]: toggleFilterReducer,
  [LoadListModuleActionTypes.SET_RESPONSE]: setResponseReducer,
  [LoadListModuleActionTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
  [LoadListModuleActionTypes.REFRESH_LIST]: refreshListReducer,
  [LoadListModuleActionTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
  [LoadListModuleActionTypes.SHOW_LOADING]: showLoadingReducer,
  [LoadListModuleActionTypes.HIDE_LOADING]: hideLoadingReducer,
  [LoadListModuleActionTypes.SELECT_TAB]: setSelectedTabReducer,
  [LoadListModuleActionTypes.SET_VALUE]: setValueReducer,
  [LoadListModuleActionTypes.SET_SELECTED_ROW]: setSelectedRowReducer,
  [LoadListModuleActionTypes.UNSET_SELECTED_ROW]: unsetSelectedRowReducer,
  [LoadListModuleActionTypes.APPLY_FILTER]: applyFilterReducer,
}

export default createReducer(LOAD_LISTING_MODULE_STATE, ACTION_HANDLERS)
