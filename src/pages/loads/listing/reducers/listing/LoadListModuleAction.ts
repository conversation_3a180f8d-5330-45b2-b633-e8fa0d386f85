import { LoadListDetails } from '@/api/types'
import LoadListModuleActionTypes from '@/pages/loads/listing/reducers/listing/LoadListModuleActionTypes'
import { LoadListingModuleState } from '@/pages/loads/listing/LoadListingModels'

export const toggleFilter = () => ({
  type: LoadListModuleActionTypes.TOGGLE_FILTER,
})

export const setSelectedTab = (tabIndex: number) => {
  return {
    type: LoadListModuleActionTypes.SELECT_TAB,
    tabIndex,
  }
}

export const setResponse = (
  response: LoadListDetails,
  selectedTabIndex: number,
  isHomePage: boolean = true
) => ({
  type: LoadListModuleActionTypes.SET_RESPONSE,
  response,
  selectedTabIndex,
  isHomePage,
})

export const setCurrentPage = (pageNumber: number) => ({
  type: LoadListModuleActionTypes.SET_CURRENT_PAGE,
  pageNumber,
})

export const refreshList = () => ({
  type: LoadListModuleActionTypes.REFRESH_LIST,
})

export const setRowPerPage = (rowsPerPage: number) => ({
  type: LoadListModuleActionTypes.SET_ROW_PER_PAGE,
  rowsPerPage,
})

export const showLoading = () => ({
  type: LoadListModuleActionTypes.SHOW_LOADING,
})

export const hideLoading = () => ({
  type: LoadListModuleActionTypes.HIDE_LOADING,
})

export const setValue = (
  value: LoadListingModuleState[keyof LoadListingModuleState],
  valueType: keyof LoadListingModuleState
) => ({
  type: LoadListModuleActionTypes.SET_VALUE,
  value,
  valueType,
})

export const setSelectedRow = (selectedRowIndex: number) => ({
  type: LoadListModuleActionTypes.SET_SELECTED_ROW,
  selectedRowIndex,
})

export const unsetSelectedRow = () => ({
  type: LoadListModuleActionTypes.UNSET_SELECTED_ROW,
})

export const applyFilter = () => ({
  type: LoadListModuleActionTypes.APPLY_FILTER,
})
