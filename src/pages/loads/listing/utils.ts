import { loadsListingAPIStatusEnum } from '@/constant/ArrayList'

export const getLoadBoardListingTabStatus = (tabIndex: number) => {
  let status = ''
  switch (tabIndex) {
    case 0:
      status = loadsListingAPIStatusEnum.PENDING
      break
    case 1:
      status = loadsListingAPIStatusEnum.REJECTED
      break
    case 2:
      status = loadsListingAPIStatusEnum.APPROVED
      break
    default:
      status = ''
  }
  return status
}
