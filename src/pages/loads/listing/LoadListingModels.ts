import { LoadItemDetail } from '@/api/types'
import { Pagination } from '@/types/Pagination'
import { loadListingFilters } from '@/utils/FilterUtils'

export interface LoadListingModuleState {
  openFilter: boolean
  pagination: Pagination | undefined
  listData: LoadItemDetail[]
  selectedTab: {
    index: number
    name: string
  }
  currentPage: number
  refreshList: boolean
  loading: boolean
  pageSize: number
  selectedRowIndex: number | undefined
  filterParams: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >
  filterChips: Partial<Record<keyof typeof loadListingFilters, string>>
}

export type LoadListFilterQueryParams = {
  status: string
  page: number
  page_size: number
  loadboard_id?: string
  customerName?: string
  origin?: string
  destination?: string
  load_type?: 'REGULAR' | 'NEW'
  indentType?: 'SCHEDULED' | 'AD_HOC'
  vehicleType?: string
  vehicleNumber?: string
}
