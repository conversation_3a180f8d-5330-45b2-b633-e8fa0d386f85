import { OptionType } from '@/components/widgets/widgetsInterfaces'
import { loadListingFilters } from '@/utils/FilterUtils'

export interface LoadListingFilterState {
  filterParams: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >
  filterValues: Partial<Record<keyof typeof loadListingFilters, string>>
  optionLists: LoadListingFilterOptionLists
  isLoadingOptions: Partial<Record<keyof LoadListingFilterOptionLists, boolean>>
  isFilterChanged: boolean
  selectedCustomerId: number | undefined
  error: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >
}
export interface LoadListingFilterOptionLists {
  customers: OptionType[]
  origins: OptionType[]
  destinations: OptionType[]
  vehicleTypes: OptionType[]
  vehicleNumbers: OptionType[]
  loadTypes: OptionType[]
  indentTypes: OptionType[]
}
