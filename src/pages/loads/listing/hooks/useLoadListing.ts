import { Dispatch as ReduxDispatch } from '@reduxjs/toolkit'
import { Dispatch, useEffect, useMemo, useReducer } from 'react'
import LoadListReducer, {
  LOAD_LISTING_MODULE_STATE,
} from '@/pages/loads/listing/reducers/listing/LoadListReducer'
import {
  LoadListFilterQueryParams,
  LoadListingModuleState,
} from '@/pages/loads/listing/LoadListingModels'
import { convertWordToTitleCase, isObjectEmpty } from '@/utils/StringUtils'
import {
  hideLoading,
  setResponse,
  showLoading,
} from '@/pages/loads/listing/reducers/listing/LoadListModuleAction'
import { useSearchParams } from '@/hooks/useSearchParams'
import { loadListingFilters } from '@/utils/FilterUtils'
import { setHeaderMenu } from '@/redux/slices/AppSlice'
import {
  headerMenuButtons,
  loadsListingStatusArray,
} from '@/constant/ArrayList'
import { getLoadListing } from '@/api/serviceActions/LoadListingActions'
import { useAsyncThunk } from '@/hooks/useAsyncThunk'
import { LoadListDetails } from '@/api/types'
import { getLoadBoardListingTabStatus } from '@/pages/loads/listing/utils'
import { useLocation } from 'react-router-dom'

type UseLoadReturnType = [
  state: LoadListingModuleState,
  dispatch: Dispatch<any>,
  //   (actionType: InvoiceListingActionType, payload?: any) => void,
]
const useLoadListing = (
  appDispatch: ReduxDispatch,
  statusParams: string
): UseLoadReturnType => {
  const location = useLocation()

  const [state = LOAD_LISTING_MODULE_STATE, dispatch] = useReducer(
    LoadListReducer,
    LOAD_LISTING_MODULE_STATE
  )

  const { currentPage, pageSize } = state

  const filterState = useSearchParams(loadListingFilters)[0]
  const filterStateParamsStr = JSON.stringify(filterState.params)

  const queryParams = useMemo(() => {
    let params: LoadListFilterQueryParams = {
      page: currentPage,
      page_size: pageSize,
      status: getLoadBoardListingTabStatus(
        loadsListingStatusArray.indexOf(convertWordToTitleCase(statusParams))
      ),
    }

    if (!isObjectEmpty(filterState.params)) {
      params = { ...params, ...filterState.params }
    }

    return params
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, pageSize, statusParams, filterStateParamsStr])

  const { execute: fetchLoadListing } = useAsyncThunk<
    LoadListDetails,
    LoadListFilterQueryParams
  >(getLoadListing, {
    immediate: false,
  })

  useEffect(() => {
    appDispatch(setHeaderMenu(headerMenuButtons[0].label))
  }, [appDispatch])

  useEffect(() => {
    const fetchLoadList = async () => {
      dispatch(showLoading())
      try {
        const response = await fetchLoadListing(queryParams)
        if (response) {
          dispatch(
            setResponse(
              response,
              loadsListingStatusArray.indexOf(
                convertWordToTitleCase(statusParams)
              )
            )
          )
        }
      } catch (error) {
        console.error('Error fetching load listing:', error)
      } finally {
        dispatch(hideLoading())
      }
    }
    fetchLoadList()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    appDispatch,
    fetchLoadListing,
    queryParams,
    state.currentPage,
    state.pageSize,
    location.search,
  ])

  return [state, dispatch]
}

export default useLoadListing
