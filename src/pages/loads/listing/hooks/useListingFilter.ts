import { Dispatch, useEffect, useReducer, useRef } from 'react'
import { loadListingFilters } from '@/utils/FilterUtils'
import ListingFilterReducer, {
  LISTING_FILTER_STATE,
} from '@/pages/loads/listing/reducers/listingFilter/ListingFilterReducer'
import {
  setAutoCompleteOptionsList,
  setFilterValuesAndParams,
  setIsLoadingFilterOptions,
} from '@/pages/loads/listing/reducers/listingFilter/ListingFilterActions'
import { LoadListingFilterState } from '@/pages/loads/listing/types/ListingFilterModel'
import { useAsyncThunk } from '@/hooks/useAsyncThunk'
import {
  getAllLoadTypes,
  getCustomers,
  getIndentTypes,
  getLocations,
  getVehicleTypes,
} from '@/api/serviceActions/CreateLoadServiceActions'
import {
  setAutoCompleteList,
  setAutoCompleteListWithData,
  setAutoCompleteListWithLabelConvertorAndValue,
} from '@/utils/DataUtils'
import { isNullValue } from '@/utils/StringUtils'
import { createFormattedOriginsOptions } from '@/utils/SharedUtils'

const useListingFilter = (
  open: boolean,
  filterState: {
    chips: Partial<Record<keyof typeof loadListingFilters, string>>
    params: Partial<
      Record<
        (typeof loadListingFilters)[keyof typeof loadListingFilters],
        string
      >
    >
  }
): [state: LoadListingFilterState, dispatch: Dispatch<any>] => {
  const [state = LISTING_FILTER_STATE, dispatch] = useReducer(
    ListingFilterReducer,
    LISTING_FILTER_STATE
  )

  const { optionLists, filterParams } = state

  const selectedCustomerId = (filterParams as { customer_id?: number })
    ?.customer_id
  const seletedOrigin = (filterParams as { origin?: string })?.origin

  const previousCustomerRef = useRef<number | undefined>(undefined)

  const apiCalls = {
    loadTypes: useAsyncThunk(getAllLoadTypes, { immediate: false }),
    vehicleTypes: useAsyncThunk(getVehicleTypes, { immediate: false }),
    indentTypes: useAsyncThunk(getIndentTypes, { immediate: false }),
    customers: useAsyncThunk(getCustomers, { immediate: false }),
    origins: useAsyncThunk(getLocations, { immediate: false }),
  }

  useEffect(() => {
    if (open) {
      dispatch(
        setFilterValuesAndParams(filterState.chips, filterState.params, false)
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  useEffect(() => {
    const fetchAutoCompleteOptions = async () => {
      try {
        const fetchTasks: Array<{
          type: string
          fetch: () => Promise<any>
        }> = []

        if (!optionLists.loadTypes.length) {
          fetchTasks.push({
            type: 'loadTypes',
            fetch: apiCalls.loadTypes.execute,
          })
          dispatch(setIsLoadingFilterOptions(['loadTypes'], true))
        }

        if (!optionLists.indentTypes.length) {
          fetchTasks.push({
            type: 'indentTypes',
            fetch: apiCalls.indentTypes.execute,
          })
          dispatch(setIsLoadingFilterOptions(['indentTypes'], true))
        }

        if (!optionLists.vehicleTypes.length) {
          fetchTasks.push({
            type: 'vehicleTypes',
            fetch: apiCalls.vehicleTypes.execute,
          })
          dispatch(setIsLoadingFilterOptions(['vehicleTypes'], true))
        }

        if (!optionLists.customers.length) {
          fetchTasks.push({
            type: 'customers',
            fetch: apiCalls.customers.execute,
          })
          dispatch(setIsLoadingFilterOptions(['customers'], true))
        }

        if (
          !isNullValue(selectedCustomerId) &&
          previousCustomerRef.current !== selectedCustomerId
        ) {
          previousCustomerRef.current = selectedCustomerId
          fetchTasks.push({
            type: 'origins',
            fetch: () => apiCalls.origins.execute({ id: selectedCustomerId! }),
          })
          dispatch(setIsLoadingFilterOptions(['origins'], true))
        }

        //TODO: Ferch Vehicle numbers for approved status
        if (fetchTasks.length > 0) {
          const results = await Promise.allSettled(
            fetchTasks.map((item) => item.fetch())
          )

          results.forEach((result, index) => {
            const { type } = fetchTasks[index]

            if (result.status === 'rejected') {
              console.error(`Error fetching ${type}:`, result.reason)
              return
            }

            const data = result.value

            if (!Array.isArray(data)) {
              console.warn(`Expected array for ${type}, got:`, typeof result)
              return
            }

            switch (type) {
              case 'loadTypes':
                dispatch(
                  setAutoCompleteOptionsList(
                    'loadTypes',
                    setAutoCompleteList(data, 'name', 'name')
                  )
                )
                break

              case 'indentTypes':
                dispatch(
                  setAutoCompleteOptionsList(
                    'indentTypes',
                    setAutoCompleteList(data, 'name', 'name')
                  )
                )
                break

              case 'vehicleTypes':
                dispatch(
                  setAutoCompleteOptionsList(
                    'vehicleTypes',
                    setAutoCompleteListWithData(data, 'name', 'name')
                  )
                )
                break

              case 'customers':
                dispatch(
                  setAutoCompleteOptionsList(
                    'customers',
                    setAutoCompleteListWithLabelConvertorAndValue(
                      data,
                      (customer) =>
                        `${customer.companyName}<${customer.customerCode}>`,
                      'companyName'
                    )
                  )
                )
                break

              case 'origins': {
                const formattedOrigins = createFormattedOriginsOptions(
                  data || []
                )

                dispatch(
                  setAutoCompleteOptionsList('origins', formattedOrigins)
                )
                break
              }

              default:
                console.warn(`Unknown result type: ${type}`)
            }
          })
        }
      } catch (error) {
        console.error('Error fetching dependent auto-complete options:', error)
      } finally {
        dispatch(
          setIsLoadingFilterOptions(
            [
              'indentTypes',
              'customers',
              'origins',
              'loadTypes',
              'vehicleTypes',
            ],
            false
          )
        )
      }
    }
    if (open) {
      fetchAutoCompleteOptions()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, selectedCustomerId, optionLists])

  useEffect(() => {
    if (open && seletedOrigin && optionLists.origins.length) {
      dispatch(
        setAutoCompleteOptionsList(
          'destinations',
          setAutoCompleteListWithData(
            optionLists.origins.find((origin) => origin.value === seletedOrigin)
              ?.data?.destinations || [],
            'label',
            'label'
          )
        )
      )
    }
  }, [
    open,
    seletedOrigin,
    optionLists.destinations.length,
    optionLists.origins,
  ])

  return [state, dispatch]
}

export default useListingFilter
