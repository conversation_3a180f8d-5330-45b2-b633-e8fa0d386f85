import PageContainer from '@/components/pageContainer/PageContainer'
import CardList from '@/components/widgets/cardlist/CardList'
import TableList from '@/components/widgets/tableView/TableList'
import { getLoadsListingColumns } from '@/templates/LoadsListingTemplates'
import { isMobile } from '@/utils/ViewUtils'
import {
  setCurrentPage,
  setSelectedRow,
} from '@/pages/loads/listing/reducers/listing/LoadListModuleAction'
import { rowsPerPageOptions } from '@/constant/ArrayList'
import { LoadItemDetail } from '@/api/types'
import { Dispatch } from 'react'
import { Pagination } from '@/types/Pagination'

interface ListingContentProps {
  listData: LoadItemDetail[]
  loading: boolean
  currentPage: number
  rowsPerPage: number
  pagination: Pagination | undefined
  dispatch: Dispatch<any>
  selectedTabIndex: number
  onChangePage: (
    event: React.MouseEvent<HTMLButtonElement> | null,
    page: number
  ) => void
  onRowsPerPageChange: React.ChangeEventHandler<
    HTMLInputElement | HTMLTextAreaElement
  >
}

export const ListingContent = ({
  listData,
  loading,
  currentPage,
  rowsPerPage,
  pagination,
  dispatch,
  selectedTabIndex,
  onChangePage,
  onRowsPerPageChange,
}: ListingContentProps) => {
  return (
    <PageContainer className="p-0" loading={loading} listData={listData}>
      {isMobile ? (
        <CardList
          listData={listData}
          tableColumns={getLoadsListingColumns(selectedTabIndex)}
          isNextPage={pagination?.next ? true : false}
          onReachEnd={() => {
            dispatch(setCurrentPage(pagination?.next || 1))
          }}
          onClickCard={(_row: any, listItemIndex?: number) => {
            dispatch(setSelectedRow(listItemIndex!))
          }}
        />
      ) : (
        <TableList
          tableColumns={getLoadsListingColumns(selectedTabIndex)}
          currentPage={currentPage}
          rowsPerPage={rowsPerPage}
          rowsPerPageOptions={rowsPerPageOptions}
          totalCount={pagination?.count}
          listData={listData}
          onChangePage={onChangePage}
          onRowsPerPageChange={onRowsPerPageChange}
          onRowClick={(_row: any, listItemIndex: number) => {
            dispatch(setSelectedRow(listItemIndex))
          }}
        />
      )}
    </PageContainer>
  )
}

export default ListingContent
