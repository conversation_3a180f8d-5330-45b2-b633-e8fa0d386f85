import { useAppDispatch } from '@/redux/store'
import { ListingTabs } from '@/pages/loads/listing/components/ListingTabs'
import { TabPanel } from '@/components/tabs/TabPanel'
import ListingContent from '@/pages/loads/listing/components/ListingContent'
import {
  applyFilter,
  setCurrentPage,
  setRowPerPage,
  setSelectedTab,
  toggleFilter,
  unsetSelectedRow,
} from '@/pages/loads/listing/reducers/listing/LoadListModuleAction'
import { useNavigate, useParams } from 'react-router-dom'
import { ListingFilters } from '@/pages/loads/listing/components/ListingFilters'
import { handleDeleteChips, loadListingFilters } from '@/utils/FilterUtils'
import HeaderSection from '@/pages/loads/listing/components/HeaderSection'
import PageContainer from '@/components/pageContainer/PageContainer'
import CardComponent from '@/components/cardComponent/CardComponent'
import Button from '@/components/widgets/button/Button'
import { Close } from '@mui/icons-material'
import { loadDetailsTitle } from '@/constant/MessageUtils'
import { useSearchParams } from '@/hooks/useSearchParams'
import FilterChips from '@/components/chips/FilterChips'
import useLoadListing from '@/pages/loads/listing/hooks/useLoadListing'
import { useCallback } from 'react'
import { loadsListingStatusArray } from '@/constant/ArrayList'
import { LoadDetials } from '@/pages/loads/loadDetails/components/LoadDetails'
import { isNullValue } from '@/utils/StringUtils'

const Home = () => {
  const appDispatch = useAppDispatch()
  const navigate = useNavigate()
  const { status: statusParams } = useParams()

  const [state, dispatch] = useLoadListing(appDispatch, statusParams!)
  const {
    selectedTab,
    listData,
    loading,
    currentPage,
    pageSize,
    pagination,
    openFilter,
    selectedRowIndex,
  } = state

  const { index: selectedTabIndex, name: selectedTabName } = selectedTab

  const addFiltersQueryParams = useSearchParams(loadListingFilters)[1]

  const onTabChanged = useCallback(
    (index: number) => {
      dispatch(setSelectedTab(index))
      navigate({
        pathname: `/${loadsListingStatusArray[index].toLowerCase()}`,
      })
    },
    [dispatch, navigate]
  )

  const handleCloseDetials = () => {
    dispatch(unsetSelectedRow())
  }

  function handleFilterBtnClick() {
    dispatch(toggleFilter())
  }

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="main-content col">
          <PageContainer>
            {openFilter && (
              <ListingFilters
                open={openFilter}
                onApplyFilter={(filterChips, filterParams) => {
                  addFiltersQueryParams(filterChips, filterParams)
                  dispatch(applyFilter())
                }}
                onClose={() => {
                  dispatch(toggleFilter())
                }}
                listingFilterKeys={loadListingFilters}
                selectedTab={selectedTabName.toLocaleUpperCase()}
              />
            )}

            <HeaderSection onFilterBtnClick={handleFilterBtnClick} />

            <FilterChips
              requestFiltersKeyValueObj={loadListingFilters}
              onDelete={handleDeleteChips}
            />

            <div className="page-content">
              <ListingTabs
                selectedTabIndex={selectedTab.index}
                onTabChanged={(tabIndex) =>
                  tabIndex !== selectedTab.index && onTabChanged(tabIndex)
                }
              />

              <TabPanel value={selectedTab.index} index={selectedTab.index}>
                <ListingContent
                  listData={listData}
                  loading={loading}
                  currentPage={currentPage}
                  rowsPerPage={pageSize}
                  pagination={pagination}
                  dispatch={dispatch}
                  selectedTabIndex={selectedTabIndex}
                  onChangePage={(_event: any, page: number) => {
                    dispatch(setCurrentPage(page))
                  }}
                  onRowsPerPageChange={(event: any) => {
                    dispatch(setRowPerPage(event.target.value))
                  }}
                />
              </TabPanel>
            </div>
          </PageContainer>
        </div>

        {selectedRowIndex !== undefined && (
          <CardComponent
            styleName="content-detail col-auto"
            title={loadDetailsTitle}
            subheader={
              <>
                <span
                  style={{
                    marginLeft: '4px',
                    fontWeight: 'normal',
                  }}
                >
                  {/* ({state.listData[selectedRowData.selectedRowIndex].id}) */}
                </span>
              </>
            }
            cardHeaderStyle={'detail-header sticky-top'}
            headAction={
              <Button
                className={'btn-close'}
                onClick={handleCloseDetials}
                leftIcon={<Close />}
              />
            }
          >
            {!isNullValue(state.listData?.[selectedRowIndex]?.id) && (
              <LoadDetials loadId={state.listData[selectedRowIndex!].id} />
            )}
          </CardComponent>
        )}
      </div>
    </div>
  )
}

export default Home
