import AutoComplete from '@/components/widgets/autoComplete/AutoComplete'
import EditText from '@/components/widgets/editText/EditText'
import {
  customerNameLabel,
  destinationLabel,
  indentTypeTitle,
  loadIdLabel,
  loadTypeTitle,
  originLabel,
  selectCustomerNameTitle,
  selectDestinationTitle,
  selectIndentTypeTitle,
  selectLoadTypeTitle,
  selectOriginTitle,
  vehicleNumberLabel,
  vehicleTypeLabel,
} from '@/constant/MessageUtils'

import { LoadListingFilterOptionLists } from '@/pages/loads/listing/types/ListingFilterModel'
import { loadListingFilters } from '@/utils/FilterUtils'
import { setAutoCompleteOptionsList } from '@/pages/loads/listing/reducers/listingFilter/ListingFilterActions'
import { AppDispatch } from '@/redux/store'
import { showErrorAlert } from '@/redux/slices/AppSlice'
import { loadsListingAPIStatusEnum } from '@/constant/ArrayList'
import { isNullValue } from '@/utils/StringUtils'
import { Dispatch } from 'react'

interface ListingFilterFormProps {
  filterParams: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >
  filterValues: Partial<Record<keyof typeof loadListingFilters, string>>
  error: Partial<
    Record<(typeof loadListingFilters)[keyof typeof loadListingFilters], string>
  >
  optionsList: LoadListingFilterOptionLists
  dispatch: Dispatch<any>
  appDispatch: AppDispatch
  selectedTab: string
  isLoadingOptions: Partial<Record<keyof LoadListingFilterOptionLists, boolean>>
  setValues: (
    chips: Partial<Record<keyof typeof loadListingFilters, string>>,
    params: Partial<
      Record<
        (typeof loadListingFilters)[keyof typeof loadListingFilters],
        string
      >
    >
  ) => void
}

export const ListingFilterForms = (props: ListingFilterFormProps) => {
  const {
    setValues,
    filterParams,
    filterValues,
    error,
    optionsList,
    dispatch,
    appDispatch,
    selectedTab,
    isLoadingOptions,
  } = props
  return (
    <div className="filter-form-row">
      <div className="form-group">
        <EditText
          label={loadIdLabel}
          placeholder={`Enter ${loadIdLabel}`}
          value={filterValues?.loadboard_id}
          maxLength={50}
          error={error?.loadboard_id}
          onChange={(loadId) => {
            setValues({ loadboard_id: loadId }, { loadboard_id: loadId })
          }}
        />
      </div>
      <div className="form-group" style={{ color: 'black' }}>
        <AutoComplete
          label={customerNameLabel}
          placeHolder={selectCustomerNameTitle}
          value={
            filterValues.customerLabel
              ? {
                  label: filterValues.customerLabel,
                  value: filterParams.customer_name,
                }
              : undefined
          }
          options={optionsList.customers}
          isLoading={isLoadingOptions.customers}
          error={error?.customer_name}
          onChange={(customer) => {
            setValues(
              {
                customerLabel: customer.label,
                originLabel: undefined,
                destinationLabel: undefined,
              },
              {
                customer_name: customer.value,
                customer_id: customer.data?.id,
                origin: undefined,
                destination: undefined,
              }
            )
            dispatch(setAutoCompleteOptionsList('destinations', []))
          }}
        />
      </div>
      <div className="form-group">
        <AutoComplete
          label={originLabel}
          placeHolder={selectOriginTitle}
          value={
            filterValues.originLabel
              ? {
                  label: filterValues.originLabel,
                  value: filterParams.origin,
                }
              : undefined
          }
          options={optionsList.origins}
          isLoading={isLoadingOptions.origins}
          error={error?.origin}
          onMenuOpen={() => {
            if (!optionsList.origins.length) {
              appDispatch(showErrorAlert('Please select customer first'))
            }
          }}
          onChange={(origin) => {
            setValues(
              { originLabel: origin.label, destinationLabel: undefined },
              { origin: origin.value, destination: undefined }
            )
          }}
        />
      </div>
      <div className="form-group">
        <AutoComplete
          label={destinationLabel}
          placeHolder={selectDestinationTitle}
          value={
            filterValues.destinationLabel
              ? {
                  label: filterValues.destinationLabel,
                  value: filterParams.destination,
                }
              : undefined
          }
          options={optionsList.destinations}
          isLoading={isLoadingOptions.destinations}
          error={error?.destination}
          onMenuOpen={() => {
            if (
              !optionsList.destinations.length &&
              isNullValue(filterParams.customer_name)
            ) {
              appDispatch(showErrorAlert('Please select customer first'))
            }
          }}
          onChange={(destination) => {
            setValues(
              { destinationLabel: destination.label },
              { destination: destination.value }
            )
          }}
        />
      </div>
      <div className="form-group">
        <AutoComplete
          label={loadTypeTitle}
          placeHolder={selectLoadTypeTitle}
          value={
            filterValues.load_type
              ? {
                  label: filterValues.load_type,
                  value: filterParams.load_type,
                }
              : undefined
          }
          options={optionsList.loadTypes}
          isLoading={isLoadingOptions.loadTypes}
          error={error?.load_type}
          onChange={(loadType) => {
            setValues(
              { load_type: loadType.label },
              { load_type: loadType.value }
            )
          }}
        />
      </div>
      <div className="form-group">
        <AutoComplete
          label={indentTypeTitle}
          placeHolder={selectIndentTypeTitle}
          value={
            filterValues.indent_type
              ? {
                  label: filterValues.indent_type,
                  value: filterParams.indent_type,
                }
              : undefined
          }
          options={optionsList.indentTypes}
          isLoading={isLoadingOptions.indentTypes}
          error={error.indent_type}
          onChange={(indentType) => {
            setValues(
              { indent_type: indentType.label },
              { indent_type: indentType.value }
            )
          }}
        />
      </div>
      <div className="form-group">
        <AutoComplete
          label={vehicleTypeLabel}
          placeHolder={`Select ${vehicleTypeLabel}`}
          value={
            filterValues.vehicleTypeLabel
              ? {
                  label: filterValues.vehicleTypeLabel,
                  value: filterParams.vehicle_type,
                }
              : undefined
          }
          options={optionsList.vehicleTypes}
          isLoading={isLoadingOptions.vehicleTypes}
          error={error?.vehicle_type}
          onChange={(vehicleType) => {
            setValues(
              { vehicleTypeLabel: vehicleType.label },
              { vehicle_type: vehicleType.value }
            )
          }}
        />
      </div>
      {selectedTab !== loadsListingAPIStatusEnum.PENDING && (
        <div className="form-group">
          <AutoComplete
            label={vehicleNumberLabel}
            placeHolder={`Select ${vehicleNumberLabel}`}
            value={
              filterValues.vehicleNumberLabel
                ? {
                    label: filterValues.vehicleNumberLabel,
                    value: filterParams.vehicle_number,
                  }
                : undefined
            }
            options={optionsList.vehicleNumbers}
            isLoading={isLoadingOptions.vehicleNumbers}
            error={error?.vehicle_number}
            onChange={(vehicleNumber) => {
              setValues(
                { vehicleNumberLabel: vehicleNumber.label },
                { vehicle_number: vehicleNumber.value }
              )
            }}
          />
        </div>
      )}
    </div>
  )
}
