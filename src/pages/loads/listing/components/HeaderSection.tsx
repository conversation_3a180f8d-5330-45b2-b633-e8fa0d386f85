import { AddCircle } from '@mui/icons-material'
import Filter from '@/components/filter/Filter'
import Button from '@/components/widgets/button/Button'
import { useNavigate } from 'react-router-dom'
import { createLoadTitle, loadsListingTitle } from '@/constant/MessageUtils'

interface HeaderProps {
  onFilterBtnClick: () => void
}

const HeaderSection = ({ onFilterBtnClick }: HeaderProps) => {
  const navigate = useNavigate()
  const createLoad = () => {
    navigate('/create-load')
  }
  return (
    <>
      <Filter
        pageTitle={loadsListingTitle}
        classNameBtn={'btn-white'}
        rightIcon={<img src="/images/filter.svg" />}
        onClick={onFilterBtnClick}
      >
        <Button
          title={createLoadTitle}
          className={'btn-blue'}
          leftIcon={<AddCircle />}
          onClick={createLoad}
        />
      </Filter>
    </>
  )
}

export default HeaderSection
