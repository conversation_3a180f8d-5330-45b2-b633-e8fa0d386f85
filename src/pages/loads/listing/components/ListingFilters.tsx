import { useCallback } from 'react'
import FilterContainer from '@/components/modals/FilterModal/FilterContainer'
import { ListingFilterForms } from '@/pages/loads/listing/components/ListingFilterForms'
import { useSearchParams } from '@/hooks/useSearchParams'
import { isNullValue, isObjectEmpty } from '@/utils/StringUtils'
import { useAppDispatch } from '@/redux/store'
import { loadListingFilters } from '@/utils/FilterUtils'
import {
  resetFilters,
  setError,
  setFilterValuesAndParams,
} from '@/pages/loads/listing/reducers/listingFilter/ListingFilterActions'
import { applyFilterTitle, clearTitle } from '@/constant/MessageUtils'
import useListingFilter from '@/pages/loads/listing/hooks/useListingFilter'

export interface ListingFiltersProps {
  open: boolean
  onClose: () => void
  onApplyFilter: (filterChips: any, filterParams: any) => void
  listingFilterKeys: unknown
  selectedTab: string
}
export const ListingFilters = (props: ListingFiltersProps) => {
  const { open, onClose, onApplyFilter, selectedTab } = props
  const appDispatch = useAppDispatch()

  const filterState = useSearchParams(loadListingFilters)[0]
  const [state, dispatch] = useListingFilter(open, filterState)

  const {
    filterParams,
    filterValues,
    isFilterChanged,
    error,
    optionLists,
    isLoadingOptions,
  } = state

  const setChipsAndParams = useCallback(
    (chips: any, params?: any) => {
      dispatch(setFilterValuesAndParams(chips, params))
    },
    [dispatch]
  )

  function onApply() {
    if (
      !isNullValue(filterValues.loadboard_id) &&
      isNullValue(filterParams.loadboard_id)
    ) {
      dispatch(setError({ loadboard_id: 'Enter valid Load Id' }))
      return
    }

    if (!isObjectEmpty(filterParams)) {
      if (isFilterChanged) {
        onApplyFilter(filterValues, filterParams)
      } else {
        onClose()
      }
    } else {
      dispatch(
        setError({
          loadboard_id: 'Enter valid Load Id',
        })
      )
    }
  }

  return (
    <FilterContainer
      title={applyFilterTitle}
      open={open}
      onClose={onClose}
      onApply={onApply}
      onClear={() => dispatch(resetFilters())}
      primaryButtonTitle={applyFilterTitle}
      secondaryButtonTitle={clearTitle}
    >
      <ListingFilterForms
        filterParams={filterParams}
        filterValues={filterValues}
        error={error}
        optionsList={optionLists}
        setValues={setChipsAndParams}
        dispatch={dispatch}
        appDispatch={appDispatch}
        selectedTab={selectedTab}
        isLoadingOptions={isLoadingOptions}
      />
    </FilterContainer>
  )
}
