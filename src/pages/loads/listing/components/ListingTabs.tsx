import { Tabs, Tab } from '@mui/material'
import { styled } from '@mui/material/styles'
import { loadsListingStatusArray } from '@/constant/ArrayList'
import { memo } from 'react'

interface TabsProps {
  selectedTabIndex: number
  onTabChanged: (newValue: number) => void
}

// Styled Tabs to remove indicator and apply custom active tab styles
const StyledTabs = styled(Tabs)(() => ({
  '& .MuiTabs-indicator': {
    display: 'none',
  },
  '& .MuiTab-root.Mui-selected': {
    margin: '0 20px',
    borderBottom: '3px solid #F7931E',
  },
}))

export const ListingTabs = memo(
  ({ selectedTabIndex = 1, onTabChanged }: TabsProps) => {
    return (
      <div className="tabs-nav">
        <div className="main-tabs-nav">
          <StyledTabs
            value={selectedTabIndex}
            onChange={(_event, newValue) => {
              if (newValue !== selectedTabIndex) {
                onTabChanged(newValue)
              }
            }}
            variant="scrollable"
            scrollButtons="auto"
          >
            {loadsListingStatusArray.map((element, index) => (
              <Tab key={index} label={element} />
            ))}
          </StyledTabs>
        </div>
      </div>
    )
  }
)
