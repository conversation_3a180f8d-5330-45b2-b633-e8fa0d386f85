import { useReducer, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { RootState, useAppDispatch } from '@/redux/store'

import {
  setLoadDetails,
  setLoading,
  setActionButtonLoader,
  setVendorAnalytics,
  setSelectedVendor,
  setModalData,
  setPollingLoaderValue,
  endPolling,
  setValue,
} from '@/pages/loads/loadDetails/reducers/LoadDetails/LoadDetailsAction'

import {
  getLoadDetails,
  acceptLoad,
  rejectLoad,
  fetchVendorAnalytics,
  moveToLoadBoard,
  createIndent,
} from '@/api/serviceActions/LoadDetailsAction'

import { pollStart, showSuccessAlert } from '@/redux/slices/AppSlice'
import loadDetailsReducer, {
  INITIAL_STATE,
} from '@/pages/loads/loadDetails/reducers/LoadDetails/LoadDetailsReducer'
import { shallowEqual, useSelector } from 'react-redux'
import {
  LoadDetailsActionType,
  LoadDetailsModal,
  LoadDetailsPollingActionType,
} from '@/pages/loads/loadDetails/types/LoadDetailsModels'
import { orchestrationToken } from '@/api/serviceActions/OpsWorkFlowServiceAction'
import { setFormData } from '../reducers/ApprovalConfirmation.ts/ApprovalConfirmationAction'

export const useLoadDetails = (loadId: string) => {
  const appDispatch = useAppDispatch()
  const navigate = useNavigate()

  const viewDocumentModalState = useSelector(
    (state: RootState) => state.app.viewDocumentModal,
    shallowEqual
  )

  const [state, dispatch] = useReducer(loadDetailsReducer, INITIAL_STATE)

  const { data } = state

  useEffect(() => {
    const loadType = data?.loadType?.toUpperCase()
    const value = {
      label: loadType,
      value: data?.id,
    }
    dispatch(setFormData('loadType', value))
  }, [data])

  useEffect(() => {
    const fetchData = async () => {
      dispatch(setLoading(true))
      try {
        const response = await appDispatch(getLoadDetails(loadId))
        dispatch(setLoadDetails(response.details))
      } catch (error) {
        console.error('Error fetching load details:', error)
      } finally {
        dispatch(setLoading(false))
      }
    }

    fetchData()
    dispatch(setSelectedVendor(null))
  }, [appDispatch, loadId])

  useEffect(() => {
    const fetchVendorAnalyticsData = async () => {
      const response = await appDispatch(fetchVendorAnalytics())
      const responseData = response as unknown as {
        code: number
        details: any
      }
      if (responseData?.code === 200) {
        dispatch(setVendorAnalytics(responseData?.details || []))
      }
    }
    fetchVendorAnalyticsData()
  }, [appDispatch])

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchPollingData = (params: any) => {
    return (
      appDispatch &&
      appDispatch(
        orchestrationToken({ orchestrationId: params.orchestrationId })
      ).then((response: any) => {
        return response
      })
    )
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const stopPollingData = (requestType: LoadDetailsPollingActionType) => {
    dispatch(endPolling(requestType, true))
  }

  const handleAction = useCallback(
    async (actionType: LoadDetailsActionType, _payload?: any) => {
      switch (actionType) {
        case 'REJECT_PENDING_LOAD': {
          dispatch(setActionButtonLoader(true))

          try {
            const response = await appDispatch(rejectLoad({ id: loadId }))
            if (response.code === 200) {
              dispatch(
                setModalData(LoadDetailsModal.REJECT_PENDING_LOAD, false)
              )
              navigate({
                pathname: `/rejected`,
              })
            }
          } catch (error) {
            console.error('"Failed to reject load."', error)
          } finally {
            dispatch(setActionButtonLoader(false))
          }
          break
        }

        case 'APPROVE_PENDING_LOAD': {
          dispatch(setActionButtonLoader(true))

          try {
            const response = await appDispatch(acceptLoad({ id: loadId }))
            if (response.code === 200) {
              appDispatch(showSuccessAlert('Request Successfully Approved. '))
              dispatch(
                setModalData(LoadDetailsModal.REJECT_PENDING_LOAD, false)
              )
              navigate({
                pathname: `/approved`,
              })
            }
          } catch (error) {
            console.error('"Failed to accept load."', error)
          } finally {
            dispatch(setActionButtonLoader(false))
          }
          break
        }

        case 'CREATE_INDENT': {
          const payload = { ..._payload }
          dispatch(
            setModalData(
              LoadDetailsModal.CREATE_INDENT,
              true,
              null,
              false,
              true
            )
          )

          dispatch(setValue(true, 'pollingLoader'))
          // dispatch(setActionBtnLoading('RAISE_INVOICE', true))
          try {
            const response = await appDispatch(createIndent(payload))
            if (response) {
              const pollStartObj = {
                asyncFetch: fetchPollingData,
                stopPollingData: stopPollingData,
                stopPollingLoader: stopPollingLoader,
              }

              appDispatch(
                pollStart({
                  ...pollStartObj,
                  params: { orchestrationId: response.orchestrationId },
                  requestType: 'CREATE_INDENT',
                })
              )
            }
          } catch (error) {
            dispatch(setValue(false, 'pollingLoader'))
            // dispatch(setActionBtnLoading('RAISE_INVOICE', false))
          }
          break
        }

        case 'MOVE_TO_LOAD_BOARD':
          {
            dispatch(
              setModalData(
                LoadDetailsModal.MOVE_TO_LOAD_BOARD,
                true,
                null,
                false,
                true
              )
            )
            const payload = {}
            dispatch(setPollingLoaderValue(true))
            try {
              const response = await appDispatch(moveToLoadBoard(payload))
              if (response) {
                const pollStartObj = {
                  asyncFetch: fetchPollingData,
                  stopPollingData: stopPollingData,
                  stopPollingLoader: stopPollingLoader,
                }
                appDispatch(
                  pollStart({
                    ...pollStartObj,
                    params: { orchestrationId: response.orchestrationId },
                    requestType: 'MOVE_TO_LOAD_BOARD',
                  })
                )
              } else {
                dispatch(setPollingLoaderValue(false))
              }
            } catch (error) {
              dispatch(setPollingLoaderValue(false))
            }
          }

          break

        default:
          console.warn('Unhandled action type:', actionType)
      }
    },
    [appDispatch, fetchPollingData, loadId, navigate, stopPollingData]
  )

  const stopPollingLoader = (requestType: LoadDetailsPollingActionType) => {
    dispatch(endPolling(requestType))
  }

  return {
    state,
    dispatch,
    handleAction,
    viewDocumentModalState,
  }
}
