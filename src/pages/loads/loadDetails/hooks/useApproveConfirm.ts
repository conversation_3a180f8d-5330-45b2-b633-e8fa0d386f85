import { useCallback, useEffect, useReducer } from 'react'
import {
  CreateLoadAutoCompleteOptionsType,
  CreateLoadFormDataType,
  LoadingAutoCompleteOptionsType,
} from '@/pages/createLoad/types/CreateLoadModel'
import { useAutoCompleteData } from '@/pages/createLoad/hooks/useAutoCompleteData'
import { useAutoCompleteEffects } from '@/pages/createLoad/hooks/useAutoCompleteEffects'
import ApproveConfirmModalReducer, {
  APPROVE_CONFIRM_STATE,
} from '../reducers/ApprovalConfirmation.ts/ApprovalConfirmationReducer'
import {
  setErrors,
  setFormData,
} from '../reducers/ApprovalConfirmation.ts/ApprovalConfirmationAction'
import { useApproveFormValidation } from './useApproveFormValidation'
import { setAutoCompleteListWithoutLabelAndValue } from '@/utils/DataUtils'
import { convertDateTimeServerFormat } from '@/utils/DateUtils'

type UseApproveConfirmReturnType = [
  formData: CreateLoadFormDataType,
  autoCompleteOptions: CreateLoadAutoCompleteOptionsType,
  isLoadingOptions: LoadingAutoCompleteOptionsType,
  error: Partial<Record<keyof CreateLoadFormDataType, string | undefined>>,
  handleChange: (field: keyof CreateLoadFormDataType, value: any) => void,
  handleSubmit: () => void,
  isValid: boolean,
]

const useApproveConfirm = (
  data: any,
  handleAction: any,
  type: string
): UseApproveConfirmReturnType => {
  const [state = APPROVE_CONFIRM_STATE, dispatch] = useReducer(
    ApproveConfirmModalReducer,
    APPROVE_CONFIRM_STATE
  )
  const { formData, autoCompleteOptions, isLoadingOptions, errors } = state
  const autoCompleteFetchers = useAutoCompleteData(dispatch)
  const { payload, validateForm } = useApproveFormValidation(formData)

  useAutoCompleteEffects({
    formData,
    autoCompleteOptions,
    dispatch,
    fetchers: autoCompleteFetchers,
    onNew: true,
  })

  useEffect(() => {
    const loadType = data['loadType']?.toUpperCase()

    const value = setAutoCompleteListWithoutLabelAndValue([loadType])

    dispatch(setFormData('loadType', value[0]))
    dispatch(
      setFormData(
        'placementDateTime',
        convertDateTimeServerFormat(data['placementDateTime'])
      )
    )
    dispatch(setFormData('rate', data['rate']))
  }, [data])

  const handleChange = useCallback(
    (field: keyof CreateLoadFormDataType, value: any) => {
      dispatch(setFormData(field, value))

      if (errors?.[field]) {
        dispatch(setErrors({ ...errors, [field]: undefined }))
      }
    },
    [errors]
  )

  const { isValid, errors: formErrors } = validateForm()
  const handleSubmit = () => {
    if (isValid) {
      handleAction(type, payload)
    } else {
      dispatch(setErrors(formErrors))
    }
  }

  return [
    formData,
    autoCompleteOptions,
    isLoadingOptions,
    errors,
    handleChange,
    handleSubmit,
    isValid,
  ]
}

export default useApproveConfirm
