import { CreateLoadFormDataType } from '@/pages/createLoad/types/CreateLoadModel'
import { useAppDispatch } from '@/redux/store'
import { useCallback } from 'react'
import { validateFormData } from '@/utils/ValidationUtils'
import { isObjectEmpty } from '@/utils/StringUtils'
import { showErrorAlert } from '@/redux/slices/AppSlice'
import { createIntendSchema, isLoadTypeRegular } from '@/pages/createLoad/utils'

export const useApproveFormValidation = (formData: CreateLoadFormDataType) => {
  const appDispatch = useAppDispatch()

  const validateForm = useCallback(() => {
    const {
      isValid,
      errors: formErrors,
      alertErrors,
    } = validateFormData(formData, createIntendSchema)

    if (!isObjectEmpty(alertErrors)) {
      appDispatch(showErrorAlert(alertErrors[Object.keys(alertErrors)[0]]))
    }

    return { isValid, errors: formErrors ?? {} }
  }, [appDispatch, formData])

  const createSubmissionPayload = (formData: CreateLoadFormDataType) => ({
    loadType: formData.loadType?.label,
    indentType: formData.indentType?.label,
    customerId: isLoadTypeRegular(formData)
      ? formData.customer?.value?.toString()
      : undefined,
    customerName: isLoadTypeRegular(formData)
      ? formData.customer?.data?.companyName
      : formData.customer,
    customerCode: isLoadTypeRegular(formData)
      ? formData.customer?.data?.customerCode
      : undefined,
    originId: isLoadTypeRegular(formData)
      ? formData.origin?.data?.id
      : undefined,
    originName: isLoadTypeRegular(formData)
      ? formData.origin?.data?.locationName
      : formData.origin,
    destinationId: isLoadTypeRegular(formData)
      ? formData.destination?.data?.id
      : undefined,
    destinationName: isLoadTypeRegular(formData)
      ? formData.destination?.data?.locationName
      : formData.destination,
    contractId: isLoadTypeRegular(formData)
      ? formData.contractId?.value
      : undefined,
    vehicleType: formData.vehicleType?.label,
    vehicleNumber: formData.vehicleNumber,
    placementDateTime: formData.placementDateTime,
    rate: formData.rate,
    advance: formData.advance,
    balance: formData.balance,
    loadingUnloadingCharges: formData.loadingUnloadingCharges,
    margin: formData.margin,
    marginPercent: formData.marginPercent,
    calculationOnExtraLoading: formData.calculationOnExtraLoading,
    remarks: formData.remarks,
    tat: formData.tat,
    waypoints: formData.waypoints.map((waypoint) => ({
      name: waypoint.label,
      code: waypoint.value,
    })),
  })

  const payload = createSubmissionPayload(formData)

  return { payload, validateForm }
}
