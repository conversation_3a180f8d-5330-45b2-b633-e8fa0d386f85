import { loadsListingAPIStatusEnum } from '@/constant/ArrayList'
import { LoadDetailsActionType } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

export const VALID_CONFIRMATION_ACTIONS: LoadDetailsActionType[] = [
  'REJECT_PENDING_LOAD',
  'CREATE_INDENT',
  'MOVE_TO_LOAD_BOARD',
]

export const isLoadApproved = (status: string): boolean => {
  return status?.toUpperCase() === loadsListingAPIStatusEnum.APPROVED
}
