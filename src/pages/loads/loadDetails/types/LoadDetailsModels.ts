export interface LoadDetail {
  id: number
  loadType: string
  customerName: string
  routeStops: any[]
  originName: string
  destinationName: string
  waypoints: string[]
  tat: number
  vehicleType: string
  vehicleNumber: string
  placementDateTime: string
  rate: any
  advance: number
  balance: number
  loadingUnloadingCharges: number
  margin: number
  marginPercentage: number
  calculationOnExtraLoading: string
  remarks: string
  createdAt: string
  updatedAt: string
  createdBy: string
  status: string
  driverMobileNumber: string
  buyerMobileNumber: string
  driverName: string
}

export interface VendorAnalyticsState {
  orderCount: number
  brokerCode: string
  brokerEmail: string
  brokerPhone: string
  brokerName: string
  brokerCompany: string
  contribution: number
  profitMargin: number
  totalScore: number
  inTransitFactor: number
  contributionFactor: number
  profitMarginFactor: number
  totalScoreFactor: number
}
export type LoadDetailsPollingActionBtnLoadingState = {
  [action in LoadDetailsPollingActionType]: boolean
}

export interface LoadDetailsState {
  data: LoadDetail | null
  modalState:
    | {
        type: LoadDetailsModalType
        isOpen: boolean
        data?: any
        isConfirmationModal?: boolean
        isApprovalModal?: boolean
      }
    | undefined
  loading: boolean
  actionButtonLoader: boolean
  vendorAnalyticsData: VendorAnalyticsState[] | null
  vendorAnalyticsLoading: boolean
  vendorAnalyticsError: any | null
  selectedVendor: VendorAnalyticsState | null
  actionBtnLoadingState: LoadDetailsPollingActionBtnLoadingState
  refreshList: boolean
  pollingLoader: boolean
}

export const LoadDetailsModal = {
  REJECT_PENDING_LOAD: 'REJECT_PENDING_LOAD',
  CREATE_INDENT: 'CREATE_INDENT',
  MOVE_TO_LOAD_BOARD: 'MOVE_TO_LOAD_BOARD',
  CREATE_INDENT_NEW_LOAD_TYPE: 'CREATE_INDENT_NEW_LOAD_TYPE',
  MOVE_TO_LOAD_BOARD_NEW_LOAD_TYPE: 'MOVE_TO_LOAD_BOARD_NEW_LOAD_TYPE',
  VENDOR_ANALYTICS: 'VENDOR_ANALYTICS',
  REJECT_LOAD_NOT_ACCEPTED_BY_VENDOR: 'REJECT_LOAD_NOT_ACCEPTED_BY_VENDOR',
} as const

export type LoadDetailsPollingActionType =
  | 'CREATE_INDENT'
  | 'MOVE_TO_LOAD_BOARD'

export type LoadDetailsModalType =
  (typeof LoadDetailsModal)[keyof typeof LoadDetailsModal]

export type LoadDetailsActionType =
  | Exclude<LoadDetailsModalType, 'VENDOR_ANALYTICS'>
  | 'APPROVE_PENDING_LOAD'

export type ConfirmationModalContent = {
  heading: string
  message: string
  confirmButtonTitle: string
  icon: React.ReactNode
}
