export type FormDataType = {
  customer?: string
  origin?: string
  destination?: string
  contractId?: string
  [key: string]: any
}

export type AutoCompleteOptionsType = {
  [field: string]: any[]
}

export type ApproveConfirmationState = {
  formData: FormDataType
  autoCompleteOptions: AutoCompleteOptionsType
  isLoadingOptions: { [key: string]: boolean }
  errors: { [field: string]: string | undefined }
}
