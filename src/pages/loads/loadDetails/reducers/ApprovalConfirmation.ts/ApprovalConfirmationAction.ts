import AprroveConfirmModalActionTypes from './ApprovalConfirmationActionTypes'

import {
  CreateLoadAutoCompleteOptionsType,
  CreateLoadFormDataType,
  CreateLoadState,
  LoadingAutoCompleteOptionsType,
} from '@/pages/createLoad/types/CreateLoadModel'
import { OptionType } from '@/components/widgets/widgetsInterfaces'

export const setFormData = (
  field: keyof CreateLoadFormDataType,
  value: unknown
) => {
  return {
    type: AprroveConfirmModalActionTypes.SET_FORM_DATA,
    field,
    value,
  }
}

export const setAutoCompleteOptions = (
  optionType: keyof CreateLoadAutoCompleteOptionsType,
  value: OptionType[]
) => ({
  type: AprroveConfirmModalActionTypes.SET_AUTO_COMPLETE_OPTIONS,
  optionType,
  value,
})

export const setIsLoadingOptions = (
  loadingOptionsTypes: (keyof LoadingAutoCompleteOptionsType)[],
  isLoading: boolean
) => ({
  type: AprroveConfirmModalActionTypes.SET_IS_LOADING_OPTIONS,
  loadingOptionsTypes,
  isLoading,
})

export const setValue = (value: any, valueType: keyof CreateLoadState) => ({
  type: AprroveConfirmModalActionTypes.SET_VALUE,
  value,
  valueType,
})

export const setErrors = (
  errors: Partial<Record<keyof CreateLoadFormDataType, string | undefined>>
) => ({
  type: AprroveConfirmModalActionTypes.SET_ERRORS,
  errors,
})

export const setIsPending = (value: any) => ({
  type: AprroveConfirmModalActionTypes.SET_IS_PENDING,
  value,
})

export const setIsSubmitting = (value: any) => ({
  type: AprroveConfirmModalActionTypes.SET_IS_SUBMITTING,
  value,
})
