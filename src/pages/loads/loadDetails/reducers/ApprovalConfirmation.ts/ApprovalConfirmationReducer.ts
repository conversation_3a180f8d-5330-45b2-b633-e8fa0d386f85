import {
  CreateLoadAutoCompleteOptionsType,
  CreateLoadFormDataType,
  CreateLoadState,
  CreateNewLoadFormDataType,
  CreateRegularLoadFormDataType,
  INITIAL_AUTO_COMPLETE_OPTIONS,
  INITIAL_FORM_DATA,
  LoadingAutoCompleteOptionsType,
} from '@/pages/createLoad/types/CreateLoadModel'
import CreateLoadActionTypes from '@/pages/createLoad/reducers/CreateLoadActionTypes'
import { createReducer } from 'reduxsauce'
import { OptionType } from '@/components/widgets/widgetsInterfaces'
import { isLoadTypeNew, isLoadTypeRegular } from '@/pages/createLoad/utils'

export const APPROVE_CONFIRM_STATE: CreateLoadState = {
  formData: INITIAL_FORM_DATA,
  autoCompleteOptions: INITIAL_AUTO_COMPLETE_OPTIONS,
  errors: {},
  isPending: false,
  isSubmitting: false,
  isLoadingOptions: {
    loadTypes: false,
    vehicleTypes: false,
    indentTypes: false,
    customers: false,
    locations: false,
    contractIds: false,
  },
}

const setFormDataReducer = (
  state = APPROVE_CONFIRM_STATE,
  action: {
    type: string
    field: keyof CreateLoadFormDataType
    value: unknown
  }
): CreateLoadState => {
  const { field, value } = action
  const currentFormData = state.formData

  if (field === 'loadType') {
    const changedLoadType = value as OptionType | undefined

    if (changedLoadType?.label == 'REGULAR') {
      const regularLoadFormData: CreateRegularLoadFormDataType = {
        ...currentFormData,
        loadType: changedLoadType as OptionType & { label: 'REGULAR' },
        indentType: undefined,
        customer: undefined,
        origin: undefined,
        destination: undefined,
        contractId: undefined,
        waypoints: [],
      }

      return {
        ...state,
        formData: regularLoadFormData,
      }
    } else if (changedLoadType?.label == 'NEW') {
      const newLoadFormData: CreateNewLoadFormDataType = {
        ...currentFormData,
        loadType: changedLoadType as OptionType & { label: 'NEW' },
        indentType: undefined,
        customer: undefined,
        origin: undefined,
        destination: undefined,
        contractId: undefined,
        vehicleType: undefined,
        tat: undefined,
      }

      return {
        ...state,
        formData: newLoadFormData,
      }
    } else {
      // loadType is undefined - return to initial state
      // Only preserve common fields
      const initialFormData = {
        ...INITIAL_FORM_DATA,
        ...currentFormData,
      }

      return {
        ...state,
        formData: initialFormData,
      }
    }
  }

  if (isLoadTypeNew(currentFormData)) {
    let formDataUpdates: Partial<CreateRegularLoadFormDataType> = {}
    let autoCompleteUpdates: Partial<CreateLoadAutoCompleteOptionsType> = {}

    switch (field) {
      case 'customer':
        formDataUpdates = {
          customer: value as OptionType | undefined,
          origin: undefined,
          destination: undefined,
          contractId: undefined,
          waypoints: [],
          tat: undefined,
          vehicleType: undefined,
        }
        autoCompleteUpdates = {
          origins: [],
          destinations: [],
          contractIds: [],
        }
        break

      case 'origin':
        formDataUpdates = {
          origin: value as OptionType | undefined,
          destination: undefined,
          contractId: undefined,
          waypoints: [],
          tat: undefined,
          vehicleType: undefined,
        }
        autoCompleteUpdates = {
          destinations: (value as OptionType)?.data?.destinations ?? [],
          contractIds: [],
        }
        break

      case 'destination':
        formDataUpdates = {
          destination: value as OptionType | undefined,
          contractId: undefined,
          waypoints: [],
          tat: undefined,
          vehicleType: undefined,
        }
        autoCompleteUpdates = {
          contractIds: [],
        }
        break

      case 'contractId': {
        const contractId = value as OptionType | undefined
        formDataUpdates = {
          contractId: contractId,
          waypoints:
            contractId?.data?.multipleStopage?.map((stopage: any) => ({
              label: stopage.sLocation.locationCity.name,
              value: stopage.sLocation.locationCity.cityCode,
            })) ?? [],
          tat: contractId?.data?.tat,
          vehicleType: {
            label: contractId?.data?.vehicleType?.name,
            value: contractId?.data?.vehicleType?.id,
            data: contractId?.data?.vehicleType,
          },
        }
        break
      }

      default:
        formDataUpdates = {
          [field]: value,
        } as Partial<CreateRegularLoadFormDataType>
        break
    }

    if (Object.keys(formDataUpdates).length) {
      return {
        ...state,
        formData: {
          ...(state.formData as CreateRegularLoadFormDataType),
          ...formDataUpdates,
        },
        autoCompleteOptions: {
          ...state.autoCompleteOptions,
          ...autoCompleteUpdates,
        },
      }
    }
  } else if (isLoadTypeRegular(currentFormData)) {
    let formDataUpdates: Partial<CreateNewLoadFormDataType> = {}
    let autoCompleteUpdates: Partial<CreateLoadAutoCompleteOptionsType> = {}

    switch (field) {
      case 'customer':
        formDataUpdates = {
          customer: value as string | undefined,
        }
        autoCompleteUpdates = {
          origins: [],
          destinations: [],
          contractIds: [],
          customers: [],
          indentTypes: [],
        }
        break

      case 'origin':
        formDataUpdates = {
          origin: value as string | undefined,
        }
        break

      case 'destination':
        formDataUpdates = {
          destination: value as string | undefined,
        }
        break

      default:
        formDataUpdates = {
          [field]: value,
        } as Partial<CreateNewLoadFormDataType>
        break
    }

    if (Object.keys(formDataUpdates).length) {
      return {
        ...state,
        formData: {
          ...(state.formData as CreateNewLoadFormDataType),
          ...formDataUpdates,
        },
        autoCompleteOptions: {
          ...state.autoCompleteOptions,
          ...autoCompleteUpdates,
        },
      }
    }
  }

  return state
}
const setAutoCompleteOptionsReducer = (
  state = APPROVE_CONFIRM_STATE,
  action: {
    type: string
    optionType: keyof CreateLoadAutoCompleteOptionsType
    value: OptionType[]
  }
) => {
  const { optionType, value } = action
  return {
    ...state,
    autoCompleteOptions: {
      ...state.autoCompleteOptions,
      [optionType]: value,
    },
  }
}

const setIsLoadingOptionsReducer = (
  state = APPROVE_CONFIRM_STATE,
  action: {
    type: string
    loadingOptionsTypes: (keyof LoadingAutoCompleteOptionsType)[]
    isLoading: boolean
  }
) => {
  const updatedLoadingState = { ...state.isLoadingOptions }

  action.loadingOptionsTypes.forEach((key) => {
    updatedLoadingState[key] = action.isLoading
  })

  return {
    ...state,
    isLoadingOptions: updatedLoadingState,
  }
}

const setValueReducer = (
  state = APPROVE_CONFIRM_STATE,
  action: {
    type: string
    valueType: keyof CreateLoadState
    value: any
  }
) => ({
  ...state,
  [action.valueType]: action.value,
})

const setErrorsReducer = (
  state = APPROVE_CONFIRM_STATE,
  action: {
    type: string
    errors: Partial<Record<keyof CreateLoadFormDataType, string | undefined>>
  }
) => ({
  ...state,
  errors: action.errors,
})

const setIsPendingReducer = (state = APPROVE_CONFIRM_STATE, action: any) => ({
  ...state,
  isPending: action.value,
})

const setIsSubmittingReducer = (
  state = APPROVE_CONFIRM_STATE,
  action: any
) => ({
  ...state,
  isSubmitting: action.value,
})

const ACTION_HANDLERS = {
  [CreateLoadActionTypes.SET_FORM_DATA]: setFormDataReducer,
  [CreateLoadActionTypes.SET_AUTO_COMPLETE_OPTIONS]:
    setAutoCompleteOptionsReducer,
  [CreateLoadActionTypes.SET_IS_LOADING_OPTIONS]: setIsLoadingOptionsReducer,
  [CreateLoadActionTypes.SET_VALUE]: setValueReducer,
  [CreateLoadActionTypes.SET_ERRORS]: setErrorsReducer,
  [CreateLoadActionTypes.SET_IS_PENDING]: setIsPendingReducer,
  [CreateLoadActionTypes.SET_IS_SUBMITTING]: setIsSubmittingReducer,
}

const ApproveConfirmModalReducer = createReducer(
  APPROVE_CONFIRM_STATE,
  ACTION_HANDLERS
)

export default ApproveConfirmModalReducer
