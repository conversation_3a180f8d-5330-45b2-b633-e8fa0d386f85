import { createReducer } from 'reduxsauce'
import {
  LoadDetail,
  LoadDetailsState,
  VendorAnalyticsState,
  LoadDetailsModalType,
} from '@/pages/loads/loadDetails/types/LoadDetailsModels'
import LoadDetailsActionTypes from './LoadDetailsActionTypes'

export const INITIAL_STATE: LoadDetailsState = {
  data: null,
  modalState: undefined,
  loading: false,
  actionButtonLoader: false,
  vendorAnalyticsData: null,
  vendorAnalyticsLoading: false,
  vendorAnalyticsError: null,
  selectedVendor: null,
  actionBtnLoadingState: {
    CREATE_INDENT: false,
    MOVE_TO_LOAD_BOARD: false,
  },
  refreshList: false,
  pollingLoader: false,
}

const setLoadDetailsReducer = (
  state = INITIAL_STATE,
  action: {
    type: string
    payload: LoadDetail
  }
) => ({
  ...state,
  data: action.payload,
})

const setValueReducer = (state = INITIAL_STATE, action: any) => ({
  ...state,
  [action.valueType]: action.value,
})

const showOrHideModalReducer = (
  state = INITIAL_STATE,
  action: {
    type: string
    modalType: LoadDetailsModalType
    isOpen: boolean
    modalData: any
    isConfirmationModal: boolean
    isApprovalModal: boolean
  }
) => ({
  ...state,
  modalState: {
    type: action.modalType,
    isOpen: action.isOpen,
    data: action.modalData,
    isConfirmationModal: action.isConfirmationModal,
    isApprovalModal: action.isApprovalModal,
  },
})

const setLoadingReducer = (
  state = INITIAL_STATE,
  action: {
    type: string
    loading: boolean
  }
) => ({
  ...state,
  loading: action.loading,
})

const setActionButtonLoaderReducer = (
  state = INITIAL_STATE,
  action: {
    type: string
    loading: boolean
  }
) => ({
  ...state,
  actionButtonLoader: action.loading,
})

const setVendorAnalyticsReducer = (
  state = INITIAL_STATE,
  action: {
    type: string
    vendorAnalyticsData: any
  }
) => {
  const newState = {
    ...state,
    vendorAnalyticsData: action.vendorAnalyticsData,
  }
  return newState
}

const setSelectedVendorReducer = (
  state = INITIAL_STATE,
  action: {
    type: string
    vendor: VendorAnalyticsState | null
  }
) => ({
  ...state,
  selectedVendor: action.vendor,
  modalState: undefined,
})

const setPollingLoaderReducer = (state = INITIAL_STATE, action: any) => ({
  ...state,
  pollingLoader: action.pollingLoader,
})
const setEndPollingReducer = (state = INITIAL_STATE, action: any) => {
  const { requestType, refreshPage } = action

  const updatedState = {
    ...state,
    pollingLoader: false,
    actionBtnLoadingState: {
      ...state.actionBtnLoadingState,
      [requestType]: false,
    },
    refreshList: refreshPage ? !state.refreshList : state.refreshList,
  }
  return updatedState
}

const ACTION_HANDLERS = {
  [LoadDetailsActionTypes.SET_LOAD_DETAILS]: setLoadDetailsReducer,
  [LoadDetailsActionTypes.SHOW_OR_HIDE_MODAL]: showOrHideModalReducer,
  [LoadDetailsActionTypes.SET_LOADING]: setLoadingReducer,
  [LoadDetailsActionTypes.SET_ACTION_BUTTON_LOADER]:
    setActionButtonLoaderReducer,
  [LoadDetailsActionTypes.SET_VENDOR_ANALYTICS]: setVendorAnalyticsReducer,
  [LoadDetailsActionTypes.SET_SELECTED_VENDOR]: setSelectedVendorReducer,
  [LoadDetailsActionTypes.SET_POLLING_LOADER]: setPollingLoaderReducer,
  [LoadDetailsActionTypes.END_POLLING]: setEndPollingReducer,
  [LoadDetailsActionTypes.SET_VALUE]: setValueReducer,
}

const loadDetailsReducer = createReducer(INITIAL_STATE, ACTION_HANDLERS)

export default loadDetailsReducer
