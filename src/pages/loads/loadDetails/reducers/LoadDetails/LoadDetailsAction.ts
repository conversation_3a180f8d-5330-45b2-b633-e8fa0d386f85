import {
  LoadDetailsModalType,
  LoadDetailsPollingActionType,
  LoadDetailsState,
} from '@/pages/loads/loadDetails/types/LoadDetailsModels'
import { VendorAnalyticsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'
import LoadDetailsActionTypes from './LoadDetailsActionTypes'

export const setLoadDetails = (payload: any) => ({
  type: LoadDetailsActionTypes.SET_LOAD_DETAILS,
  payload,
})

export const setModalData = (
  modalType: LoadDetailsModalType,
  isOpen: boolean,
  modalData: any = null,
  isConfirmationModal: boolean = false,
  isApprovalModal: boolean = false
) => ({
  type: LoadDetailsActionTypes.SHOW_OR_HIDE_MODAL,
  modalType,
  modalData,
  isOpen,
  isConfirmationModal,
  isApprovalModal,
})

export const setLoading = (loading: boolean) => ({
  type: LoadDetailsActionTypes.SET_LOADING,
  loading,
})

export const setValue = (value: any, valueType: keyof LoadDetailsState) => ({
  type: LoadDetailsActionTypes.SET_VALUE,
  value,
  valueType,
})

export const setActionButtonLoader = (loading: boolean) => ({
  type: LoadDetailsActionTypes.SET_ACTION_BUTTON_LOADER,
  loading,
})

export const setVendorAnalytics = (vendorAnalyticsData: any) => ({
  type: LoadDetailsActionTypes.SET_VENDOR_ANALYTICS,
  vendorAnalyticsData,
})

export const setSelectedVendor = (vendor: VendorAnalyticsState | null) => ({
  type: LoadDetailsActionTypes.SET_SELECTED_VENDOR,
  vendor,
})

export const setPollingLoaderValue = (pollingLoader: boolean) => ({
  type: LoadDetailsActionTypes.SET_POLLING_LOADER,
  pollingLoader,
})

export const endPolling = (
  requestType: LoadDetailsPollingActionType,
  refreshPage: boolean = false
) => ({
  type: LoadDetailsActionTypes.END_POLLING,
  requestType,
  refreshPage,
})
