import Styles from '@/pages/loads/loadDetails/components/LoadDetails.module.scss'
import { LocationOn, TripOrigin } from '@mui/icons-material'
import Typography from '@mui/material/Typography/Typography'

type LocationTimelineProps = {
  origin: string
  destination: string
  waypoints: string[]
}

const LocationTimeline = (props: LocationTimelineProps) => {
  const { origin, destination, waypoints } = props
  return (
    <div>
      <div className={Styles.stapper_box}>
        <ul className={Styles.stapper_list}>
          {/* Origin */}
          <li className={Styles.stapper_item}>
            <span className={Styles.stapper_icon}>
              <TripOrigin />
            </span>
            <div className={Styles.stapper_content}>
              <Typography variant="caption" className={Styles.stapper_title}>
                {origin}
              </Typography>
            </div>
          </li>

          {/* Waypoints */}
          {waypoints.map((waypoint, index) => (
            <li className={Styles.stapper_item} key={index}>
              <span className={Styles.stapper_icon}>
                <TripOrigin className={Styles.way_point} />
              </span>
              <div className={Styles.stapper_content}>
                <Typography variant="caption" className={Styles.stapper_title}>
                  {waypoint}
                </Typography>
              </div>
            </li>
          ))}

          {/* Destination */}
          <li className={Styles.stapper_item}>
            <span className={Styles.stapper_icon}>
              <LocationOn className={Styles.active} />
            </span>
            <div className={Styles.stapper_content}>
              <Typography variant="caption" className={Styles.stapper_title}>
                {destination}
              </Typography>
            </div>
          </li>
        </ul>
      </div>
    </div>
  )
}

export default LocationTimeline
