import ModalContainer from '@/components/modals/ModalContainer'
import '@/components/modals/AlertBox/MessageAlertBox'
import { HighlightOff } from '@mui/icons-material'

interface RejectConfirmModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  showSubmitLoader?: boolean
}

function RejectConfirmModal(props: RejectConfirmModalProps) {
  const { open, onClose, onConfirm, showSubmitLoader = false } = props
  return (
    <ModalContainer
      title="Confirm"
      secondaryButtonTitle="Cancel"
      primaryButtonTitle="Yes, Reject it"
      onClear={onClose}
      onApply={onConfirm}
      open={open}
      onClose={onClose}
      styleName="message-modal custom-modal error"
      primaryButtonStyle="btn-orange"
      secondaryButtonStyle="btn-grey-cancel"
      loading={showSubmitLoader}
    >
      <div className="text-center">
        <HighlightOff />
        <h2 className="content-heading error">Reject</h2>
      </div>
      <div className="text-center">
        <label>
          {/* This action will reject the load. Do you want to continue? */}
          Are you sure you want to reject this load?
        </label>
      </div>
    </ModalContainer>
  )
}

export default RejectConfirmModal
