.load_details {
  margin: 0;
  .load_Container {
    padding: 1em;
    border-radius: 0;
    border: none;
    margin-bottom: 0.5em;
  }
  .stapper_box {
    padding: 0;
    margin: 0;
    .stapper_list {
      position: relative;
      .stapper_item {
        list-style: none;
        display: flex;
        align-items: flex-start;
        position: relative;
        padding-left: 20px;
        &:not(:last-child)::before {
          content: '';
          position: absolute;
          left: 9px;
          top: 24px;
          bottom: 0;
          width: 1.6px;
          border-left: 1.6px dashed #ccc;
        }
        &:not(:last-child) {
          padding-bottom: 16px;
        }
        .stapper_icon {
          position: absolute;
          left: 0;
          top: 0;
          background: white;
          z-index: 1;
          svg {
            font-size: 20px;
            color: #707070;
            &.way_point {
              opacity: 0.6;
            }
            &.active {
              color: #f7791e;
              font-size: 24px;
              margin-left: -2px;
            }
          }
        }
        .stapper_content {
          padding-left: 10px;
          .stapper_title {
            font-size: 13px;
            font-weight: normal;
            color: #323232;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
  .vendor_review {
    margin-top: -6px;
    .vendor_status {
      background-color: rgba(82, 168, 62, 0.2);
      color: #52a83e;
      margin: 0;
      font-size: 13px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .vendor_analytics_section {
    margin-top: -6px;
    background-color: rgba($color: #f5f5f5, $alpha: 0.66);
    .vendor_title {
      padding-left: 16px;
      span {
        font-size: 14px !important;
        font-weight: normal !important;
      }
    }
    .btnAnalytics {
      border: solid 2px #e0e4e6;
      margin-left: 8px;
    }
  }
  .map_section {
    :global {
      .MuiCardHeader-title {
        white-space: nowrap;
      }
      .MuiCardHeader-subheader {
        width: 100%;
        display: block;
        margin-left: 8px;
      }
    }
    .more_info {
      white-space: nowrap;
      color: #323232;
      opacity: 0.75;
      font-size: 13px;
      font-weight: normal;
      width: 100%;
      margin: auto;
      border-left: solid 2px #ddd;
      svg {
        font-size: 18px;
      }
    }
  }
  .more_details_Section {
    :global {
      .MuiAccordionSummary-root.Mui-expanded {
        border-bottom: solid 1px #e7e7e7;
      }
      .MuiAccordionSummary-root {
        min-height: 48px;
        .MuiAccordionSummary-content {
          margin: 4px 0;
        }
      }
      .info-heading {
        font-size: 11px;
      }
      .media-body {
        font-size: 13px;
        color: #333333;
      }
    }
    &::before {
      display: none;
    }
  }
  .document_view {
    background-color: #fff;
    padding: 10px;
    margin: 8px 0 12px;
    border-radius: 4px;
    .doc_head {
      text-align: center;
      margin-bottom: 10px;
      position: relative;
      .doc_title {
        font-size: 12px;
        font-weight: 500;
        color: #1a1a1a;
        display: inline-flex;
        align-items: center;
        padding: 0 16px;
        margin: 0;
        background: white;
        position: relative;
        z-index: 2;
        &::before,
        &::after {
          content: ' ';
          position: absolute;
          left: 0;
          width: 4px;
          height: 4px;
          z-index: 1;
          background-color: #ccc;
          border-radius: 50%;
        }
        &::after {
          left: initial;
          right: 0;
        }
      }
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #e0e0e0;
        background: linear-gradient(
          to right,
          #ffffff,
          #e0e0e0,
          #e0e0e0,
          #e0e0e0,
          #e0e0e0,
          #ffffff
        );
        z-index: 1;
      }
    }
    .doc_wrap {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 8px;
      width: 100%;
      text-align: center;
      .items {
        border-radius: 4px;
        border: solid 1px #eee;
        &:nth-last-child {
          margin-left: 8px;
        }
        .info {
          text-align: center;
          :global {
            .info-heading {
              color: #333333;
              margin: auto;
              opacity: 0.8;
            }
            .media-body {
              font-size: 12px;
              color: #f7791e;
              margin: auto;
            }
          }
        }
        .content {
          margin-top: 10px;
          display: inline-flex;
          gap: 8px;
          .wrap {
            display: inline-flex;
            padding: 4px;
            flex-direction: column;
            margin: auto;
            height: auto;
            background-color: #ffffff;
            border-radius: 4px;
            svg {
              margin: auto;
              font-size: 40px;
              opacity: 0.7;
            }
            img {
              width: 30px;
              height: auto;
            }
            :global {
              .left-icon {
                margin-right: 0px !important;
              }
              .title {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}
