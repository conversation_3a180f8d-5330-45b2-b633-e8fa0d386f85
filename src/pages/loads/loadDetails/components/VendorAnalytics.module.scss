.vendor_analytics_wrap :global{
    .MuiDialog-paper{
        min-width: 890px;
        .MuiDialogContent-root{
            padding: 12px 15px;
        }
    }
     .table-list-view .table-wrapper {
        min-height: initial;
        max-height: initial;
        .MuiTableRow-root {
            .MuiTableCell-head {
                background: #f4f6f8;
                height: 29px;
                font-weight: 400;
            }
            .MuiTableCell-root{
                font-size: 12px;
            }
            // &.td-info-wrap{
            //     min-width: 330px;
            //     max-width:none;
            //     width: initial;
            // }
        }
    }
    .vendor-info{
         h6{
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 8px;
            letter-spacing: 0.12px;
            color: #333333D9;
        }
       ul{
        li{
            font-size: 12px;
            color: #798B9D;
            svg{
                font-size: 14px;
                margin-right: 5px;
            }
        }
        &.vendor-info-mail{
            margin-left: 10px;
            border-left: 1px solid #B6BFC9;
            padding-left: 10px;
            li+li{
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 180px;
            } 
        }
       }
    }
}