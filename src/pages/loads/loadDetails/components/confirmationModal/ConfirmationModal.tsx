import ModalContainer from '@/components/modals/ModalContainer'

interface ConfirmationModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  title?: string
  heading?: string
  message: string
  icon?: any
  showSubmitLoader?: boolean
  confirmButtonTitle?: string
  cancelButtonTitle?: string
  primaryButtonStyle?: string
  secondaryButtonStyle?: string
  styleName?: string
}

const ConfirmationModal = ({
  open,
  onClose,
  onConfirm,
  title = 'Confirm',
  heading = '',
  message,
  icon = undefined,
  showSubmitLoader = false,
  confirmButtonTitle = 'Confirm',
  cancelButtonTitle = 'Cancel',
  primaryButtonStyle = 'btn-orange btn-width-100 ms-3',
  secondaryButtonStyle = 'btn-grey btn-width-100',
  styleName = 'message-modal custom-modal error',
}: ConfirmationModalProps) => {
  return (
    <ModalContainer
      title={title}
      open={open}
      onClose={onClose}
      onClear={onClose}
      onApply={onConfirm}
      loading={showSubmitLoader}
      primaryButtonTitle={confirmButtonTitle}
      secondaryButtonTitle={cancelButtonTitle}
      primaryButtonStyle={primaryButtonStyle}
      secondaryButtonStyle={secondaryButtonStyle}
      styleName={styleName}
    >
      <div className="text-center">
        {icon}
        {heading && (
          <h2
            className={`content-heading ${styleName === 'reject' ? 'error' : 'black-text'}`}
          >
            {heading}
          </h2>
        )}
      </div>
      <div className="text-center">
        <label>{message}</label>
      </div>
    </ModalContainer>
  )
}

export default ConfirmationModal
