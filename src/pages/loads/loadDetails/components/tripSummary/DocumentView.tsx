import { Box } from '@mui/material'
import Styles from '../LoadDetails.module.scss'
import Information from '@/components/information/Information'
import Button from '@/components/widgets/button/Button'
import { ImageOutlined } from '@mui/icons-material'

interface DocumentViewProps {
  handleOpen: (key: string, label: string) => void
}
const DocumentView: React.FC<DocumentViewProps> = ({ handleOpen }) => {
  return (
    <div>
      <Box
        bgcolor={'white'}
        p={2}
        component={'div'}
        className={Styles.document_view}
      >
        <Box component={'div'} className={Styles.doc_head}>
          <h4 className={Styles.doc_title}>Documents</h4>
        </Box>
        <Box component={'div'} className={Styles.doc_wrap}>
          <Box
            bgcolor={'grey.100'}
            p={1}
            component={'div'}
            className={Styles.items}
          >
            <Information
              className={Styles.info}
              title="LR Receipt No."
              text="LRN98739480"
            />
            <Box component={'div'} className={Styles.content}>
              <Button
                title="NA"
                className={Styles.wrap}
                leftIcon={<ImageOutlined />}
                onClick={() => handleOpen('lr', 'LR Receipt')}
              />
            </Box>
          </Box>
          <Box
            bgcolor={'grey.100'}
            p={1}
            component={'div'}
            className={Styles.items}
          >
            <Information
              className={Styles.info}
              title="E-way Bill No."
              text="EWAY-17823468923498"
            />
            <Box component={'div'} className={Styles.content}>
              <Button
                // title="NA"
                className={Styles.wrap}
                leftIcon={<ImageOutlined />}
                onClick={() => {
                  handleOpen('eway', 'E-way Bill')
                }}
              />
            </Box>
          </Box>
          <Box
            bgcolor={'grey.100'}
            p={1}
            component={'div'}
            className={Styles.items}
          >
            <Information className={Styles.info} title="POD" text="" />
            <Box component={'div'} className={Styles.content}>
              <Button
                title="Front"
                className={Styles.wrap}
                leftIcon={<ImageOutlined />}
                onClick={() => {
                  handleOpen('podFrontBack', 'POD Front & Back')
                }}
              />
              <Button
                title="Back"
                className={Styles.wrap}
                leftIcon={<ImageOutlined />}
                onClick={() => {
                  handleOpen('podFrontBack', 'POD Front & Back')
                }}
              />
            </Box>
          </Box>
          <Box
            bgcolor={'grey.100'}
            p={1}
            component={'div'}
            className={Styles.items}
          >
            <Information
              className={Styles.info}
              title="Invoice No."
              text="INV-2342340283498"
            />
            <Box component={'div'} className={Styles.content}>
              <Button
                // title="NA"
                className={Styles.wrap}
                leftIcon={<ImageOutlined />}
                onClick={() => {
                  handleOpen('invoice', 'Invoice')
                }}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </div>
  )
}
export default DocumentView
