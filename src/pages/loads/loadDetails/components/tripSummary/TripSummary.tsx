import CardComponent from '@/components/cardComponent/CardComponent'
import Styles from '@/pages/loads/loadDetails/components/LoadDetails.module.scss'
import {
  AccessTime,
  Group,
  LocalShipping,
  LocationOn,
  MyLocation,
  Speed,
} from '@mui/icons-material'
import Box from '@mui/material/Box/Box'
import MoreDetails from '@/pages/loads/loadDetails/components/tripSummary/MoreDetails'
import { LoadDetailsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

type TripSummaryProps = {
  handleDocumentOpen: (key: string, label: string) => void
  state: LoadDetailsState
}
const TripSummary: React.FC<TripSummaryProps> = ({
  handleDocumentOpen,
  state,
}) => {
  return (
    <CardComponent
      styleName={`${Styles.load_Container} ${Styles.map_section} pt-0`}
      title={'2 Stop'}
      subheader={
        <>
          <div className={`row ${Styles.more_info}`}>
            <div className="col ps-2">
              <MyLocation className="me-2" />
              <span>Jaipur</span>
            </div>
            <div className="col-auto pe-0">
              <span>1190 km</span>
            </div>
          </div>
        </>
      }
    >
      <Box
        display={'flex'}
        alignItems={'center'}
        border={1}
        borderColor={'#ddd'}
        p={1}
      >
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d112130.79223028432!2d77.**************!3d28.5671424!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2sin!4v1745574181304!5m2!1sen!2sin"
          width="100%"
          height="280"
          loading="lazy"
        ></iframe>
      </Box>

      <Box
        marginTop={2}
        color={'#333333'}
        sx={{
          opacity: 0.86,
          display: 'grid',
          fontSize: '13px',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: 1,
        }}
      >
        <Box>
          <LocationOn className="me-1" />
          <span>2 Stop</span>
        </Box>
        <Box>
          <LocalShipping className="me-1" />
          <span>{state?.data?.vehicleType}</span>
        </Box>
        <Box>
          <Speed className="me-1" />
          <span>1190 km</span>
        </Box>
        <Box>
          <AccessTime className="me-1" />
          <span>20h 10m</span>
        </Box>
        <Box>
          <Group className="me-1" />
          <span>Team</span>
        </Box>
      </Box>

      {/* More Details */}
      <MoreDetails
        onDocumentClick={handleDocumentOpen}
        loadDetailsData={state}
      />
    </CardComponent>
  )
}
export default TripSummary
