import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from '@mui/material'
import Styles from '../LoadDetails.module.scss'
import DocumentView from './DocumentView'
import LoadDetailsInfoGrid from './LoadDetailsInfoGrid'
import { ExpandMore } from '@mui/icons-material'
import { LoadDetailsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

interface MoreDetailsProps {
  onDocumentClick: (key: string, label: string) => void
  loadDetailsData: LoadDetailsState
}
const MoreDetails: React.FC<MoreDetailsProps> = ({
  onDocumentClick,
  loadDetailsData,
}) => {
  return (
    <div>
      <Accordion
        defaultExpanded={false}
        className={`${Styles.more_details_Section}`}
        sx={{
          mt: 2,
          bgcolor: 'grey.100',
          boxShadow: 'none',
          border: 'none',
          borderRadius: '3px',
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMore />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography component="span">More Details</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <DocumentView handleOpen={onDocumentClick} />
          <LoadDetailsInfoGrid loadDetailsData={loadDetailsData} />
        </AccordionDetails>
      </Accordion>
    </div>
  )
}
export default MoreDetails
