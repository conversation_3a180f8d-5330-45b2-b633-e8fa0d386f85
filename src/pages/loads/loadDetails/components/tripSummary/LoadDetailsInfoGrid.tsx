import Information from '@/components/information/Information'
import { Box } from '@mui/material'
import React from 'react'
import { LoadDetailsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

interface LoadDetailsInfoGridProps {
  loadDetailsData: LoadDetailsState
}
const LoadDetailsInfoGrid: React.FC<LoadDetailsInfoGridProps> = ({
  loadDetailsData,
}) => {
  return (
    <>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, minmax(0, 1fr))',
          gap: 1,
          width: '100%',
        }}
      >
        <Information
          title={'Driver Name'}
          text={loadDetailsData?.data?.driverName}
        />
        <Information
          title={'Driver Number'}
          text={loadDetailsData?.data?.driverMobileNumber}
        />
        <Information
          title={'Buyer Mobile Number'}
          text={loadDetailsData?.data?.buyerMobileNumber}
        />
        <Information
          title={'Placement Date & Time'}
          text={loadDetailsData?.data?.placementDateTime}
        />
        <Information
          title={'Vehicle Number'}
          text={loadDetailsData?.data?.vehicleNumber}
        />
        <Information
          title={'Vendor Name'}
          text={'Dynamics Logistics Pvt. Ltd. Dynamics Logistics Pvt. Ltd.'}
        />
        <Information title={'Vendor Code'} text={'VDO00128'} />
      </Box>
    </>
  )
}
export default LoadDetailsInfoGrid
