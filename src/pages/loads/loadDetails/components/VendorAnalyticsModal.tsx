import ModalContainer from '@/components/modals/ModalContainer'
import TableList from '@/components/widgets/tableView/TableList'
import { rowsPerPageOptions } from '@/constant/ArrayList'
import { getVendorAnalyticsColumns } from '@/templates/VendorAnalyticsTemplates'
import Styles from './VendorAnalytics.module.scss'
import { VendorAnalyticsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

interface VendorAnalyticsModalProps {
  open: boolean
  onClose: () => void
  vendorId?: string
  analyticsData?: any | null
  loading?: boolean
  onVendorSelect: (vendor: any) => void
  selectedVendor: VendorAnalyticsState | null
}

function VendorAnalyticsModal(props: VendorAnalyticsModalProps) {
  const {
    open,
    onClose,
    analyticsData = [],
    onVendorSelect,
    selectedVendor,
  } = props
  return (
    <ModalContainer
      title="Vendor Analytics"
      open={open}
      onClose={onClose}
      styleName={Styles.vendor_analytics_wrap}
    >
      <TableList
        tableColumns={getVendorAnalyticsColumns(selectedVendor, onVendorSelect)}
        currentPage={0}
        rowsPerPage={0}
        rowsPerPageOptions={rowsPerPageOptions}
        totalCount={100}
        listData={analyticsData}
        onChangePage={() => {}}
        onRowsPerPageChange={() => {}}
      />
    </ModalContainer>
  )
}

export default VendorAnalyticsModal
