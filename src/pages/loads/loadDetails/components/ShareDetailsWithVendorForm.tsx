import CardComponent from '@/components/cardComponent/CardComponent'
import Styles from '@/pages/loads/loadDetails/components/LoadDetails.module.scss'
import { Box, Typography } from '@mui/material'
import AutoComplete from '@/components/widgets/autoComplete/AutoComplete'
import Button from '@/components/widgets/button/Button'
import EditText from '@/components/widgets/editText/EditText'
import { ArrowForward, InfoOutlined } from '@mui/icons-material'
import { VendorAnalyticsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

interface ShareDetailsWithVendorFormProps {
  handleOpenVendorAnalytics: () => void
  vendorAnalyticsData: VendorAnalyticsState[] | null
  selectedVendor: VendorAnalyticsState | null
  onVendorSelect: (vendor: VendorAnalyticsState | null) => void
}

const ShareDetailsWithVendorForm: React.FC<ShareDetailsWithVendorFormProps> = ({
  handleOpenVendorAnalytics,
  vendorAnalyticsData,
  selectedVendor,
  onVendorSelect,
}) => {
  const autoCompleteOptions = vendorAnalyticsData
    ? vendorAnalyticsData.map((vendor) => ({
        label: `${vendor.brokerName}<${vendor.brokerCode}>`,
        value: vendor.brokerCode,
        data: vendor,
      }))
    : []
  return (
    <CardComponent
      styleName={`${Styles.load_Container} ${Styles.vendor_analytics_section} p-0`}
      title="Share details with vendor"
      cardHeaderStyle={Styles.vendor_title}
    >
      <Box p={2} className="pt-0">
        <div className="row g-0 mb-1">
          <div className="col">
            <AutoComplete
              placeHolder="Select Vendor"
              value={
                selectedVendor
                  ? {
                      label: selectedVendor.brokerName,
                      value: selectedVendor.brokerCode,
                      data: selectedVendor,
                    }
                  : null
              }
              error={''}
              options={autoCompleteOptions}
              onChange={(newValue: any) => {
                onVendorSelect(newValue ? newValue.data : null)
              }}
            />
          </div>
          <div className="col-auto">
            <Button
              className={Styles.btnAnalytics}
              height={44}
              leftIcon={
                <img
                  src="/images/vendor-analytics.svg"
                  alt="Vendor Analytics"
                />
              }
              onClick={handleOpenVendorAnalytics}
              leftIconTooltip={{
                show: true,
                text: 'Vendor Analytics',
                placement: 'top',
                styles: {
                  backgroundColor: '#083654 !important',
                  textColor: '#fff',
                  arrowColor: '#083654',
                },
              }}
            />
          </div>
        </div>
        <EditText
          className="bg-white"
          placeholder="Enter Rate"
          value={''}
          maxLength={50}
          onChange={() => {}}
        />
        <Typography
          variant="subtitle1"
          color="text.secondary"
          fontSize="13px"
          className="my-2"
        >
          <InfoOutlined style={{ fontSize: 15, color: '#F2883C' }} /> Rate
          should not be less ₹31808
        </Typography>
        <Button
          title="Submit"
          rightIcon={<ArrowForward />}
          className="btn-blue"
          onClick={() => {}}
          fullWidth
        />
      </Box>
    </CardComponent>
  )
}

export default ShareDetailsWithVendorForm
