import React from 'react'
import { Box, Chip, TextField, InputAdornment } from '@mui/material'
import HighlightOff from '@mui/icons-material/HighlightOff'
import Styles from '@/pages/loads/loadDetails/components/LoadDetails.module.scss'
import CardComponent from '@/components/cardComponent/CardComponent'
import Information from '@/components/information/Information'
import Button from '@/components/widgets/button/Button'
import { LoadDetailsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'

interface VendorInfoSectionProps {
  rejectTitle: string
  loadDetailsData: LoadDetailsState
}

const VendorInfoSection: React.FC<VendorInfoSectionProps> = ({
  rejectTitle,
  loadDetailsData,
}) => {
  return (
    <CardComponent
      styleName={`${Styles.load_Container} ${Styles.vendor_review} pt-0 px-0`}
    >
      <div className="row g-0">
        <div className="d-flex justify-content-between">
          <Box component="h4" className={`${Styles.vendor_status} w-100`}>
            Waiting for Acceptance
          </Box>
          <Button
            title={rejectTitle}
            radius="radius-none"
            className="btn-red-reject"
            leftIcon={<HighlightOff />}
            onClick={() => {}}
          />
        </div>
      </div>
      <Box p={2} className="pb-0">
        <div className="row">
          <Information
            className="col border-end font-weight-bold"
            title="Vendor Rate"
            text={loadDetailsData.data?.rate}
            valueClassName="orange-color"
            rightInfo={
              <Chip
                label={loadDetailsData?.data?.loadType}
                color="error"
                sx={{
                  borderRadius: '3px',
                  fontSize: '11px',
                  height: '20px',
                  '& .MuiChip-label': {
                    padding: '0 6px',
                  },
                }}
              />
            }
          />
          <Information
            className="col"
            title="Vendor Name"
            text="Dynamic trans Solution"
          />
        </div>
        <div className="d-flex justify-content-between gap-3 mt-3">
          <Button
            className="btn-blue btn-sm"
            title="Counter Rate"
            onClick={() => {}}
            fullWidth
          />
          <Button
            className="btn-blue btn-sm"
            title="Accept"
            onClick={() => {}}
            fullWidth
          />
        </div>
        <Box
          position="relative"
          bgcolor="grey.100"
          p={2}
          sx={{
            display: 'flex',
            gap: 1,
            mt: 2,
          }}
        >
          <Button
            className="p-0 bg-white position-absolute"
            buttonStyle={{ top: '-4px', right: '-4px', height: 'initial' }}
            radius="radius-rounded"
            leftIcon={<HighlightOff />}
            onClick={() => {}}
          />
          <TextField
            sx={{ backgroundColor: 'white' }}
            size="small"
            label="Enter Counter Rate"
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">₹</InputAdornment>
              ),
            }}
          />
          <Button title="Submit" className="btn-blue" onClick={() => {}} />
        </Box>
      </Box>
    </CardComponent>
  )
}

export default VendorInfoSection
