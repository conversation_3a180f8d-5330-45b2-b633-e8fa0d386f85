import Button from '@/components/widgets/button/Button'
import { approveTitle, rejectTtile } from '@/constant/MessageUtils'
import HighlightOff from '@mui/icons-material/HighlightOff'
import Divider from '@mui/material/Divider/Divider'
import { Dispatch } from 'react'
import { loadsListingAPIStatusEnum } from '@/constant/ArrayList'
import { CheckCircle, OpenWith } from '@mui/icons-material'
import { setModalData } from '../reducers/LoadDetails/LoadDetailsAction'
import {
  LoadDetailsActionType,
  LoadDetailsModal,
} from '../types/LoadDetailsModels'

type ActionButtonsProps = {
  status: string
  dispatch: Dispatch<any>
  handleAction: (actionType: LoadDetailsActionType, payload?: any) => void
  actionButtonLoader: boolean
}

const ActionButtons = ({
  status,
  dispatch,
  handleAction,
  actionButtonLoader,
}: ActionButtonsProps) => {
  return (
    <>
      {status.toUpperCase() === loadsListingAPIStatusEnum.PENDING && (
        <>
          <Divider sx={{ my: 1, bgcolor: 'grey.500' }} />
          <div className="d-flex justify-content-between gap-2">
            <Button
              title={rejectTtile}
              className={'btn-red-reject'}
              leftIcon={<HighlightOff />}
              fullWidth
              onClick={() => {
                dispatch(
                  setModalData(
                    LoadDetailsModal.REJECT_PENDING_LOAD,
                    true,
                    null,
                    true
                  )
                )
              }}
            />
            <Button
              title={approveTitle}
              className={'btn-blue'}
              leftIcon={<CheckCircle />}
              fullWidth
              onClick={() => {
                handleAction('APPROVE_PENDING_LOAD')
              }}
              loading={actionButtonLoader}
            />
          </div>
        </>
      )}

      {status.toUpperCase() === loadsListingAPIStatusEnum.APPROVED && (
        <>
          <Divider sx={{ my: 1, bgcolor: 'grey.500' }} />
          <div className="d-flex justify-content-between gap-2">
            <Button
              title={'Create Indent'}
              className={'btn-warning-light'}
              leftIcon={<OpenWith />}
              leftIconStyle={'orange-color'}
              fullWidth
              onClick={() => {
                dispatch(
                  setModalData(
                    LoadDetailsModal.CREATE_INDENT,
                    true,
                    null,
                    true,
                    true
                  )
                )
              }}
            />
            <Button
              title={'Move to Load Board'}
              className={'btn-blue'}
              leftIcon={<OpenWith />}
              fullWidth
              onClick={() => {
                dispatch(
                  setModalData(
                    LoadDetailsModal.MOVE_TO_LOAD_BOARD,
                    true,
                    null,
                    true,
                    true
                  )
                )
              }}
            />
          </div>
        </>
      )}
    </>
  )
}

export default ActionButtons
