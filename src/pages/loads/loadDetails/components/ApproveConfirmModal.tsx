import ModalContainer from '@/components/modals/ModalContainer'
import Styles from './ApproveConfirm.module.scss'
import EditText from '@/components/widgets/editText/EditText'
import AutoComplete from '@/components/widgets/autoComplete/AutoComplete'
import { ArrowForward, CalendarMonth } from '@mui/icons-material'
import NumberEditText from '@/components/widgets/NumberEditText'
import DatePicker from '@/components/widgets/datePicker/DatePicker'
import TextArea from '@/components/widgets/textArea/TextArea'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from '@mui/material'
import useApproveConfirm from '../hooks/useApproveConfirm'
import {
  ALPHABETIC_WITH_SPACE_REGEX,
  ALPHANUMERIC_REGEX,
} from '@/constant/ValidationConstants'
import { convertDateTimeServerFormat } from '@/utils/DateUtils'
import dayjs from 'dayjs'
import Chips from '@/components/chips/Chips'
import { LoadDetailsActionType } from '../types/LoadDetailsModels'

interface ApproveConfirmModalProps {
  open: boolean
  onClose: () => void
  data: any
  type: string
  handleAction: (actionType: LoadDetailsActionType, _payload?: any) => void
}

function ApproveConfirmModal(props: ApproveConfirmModalProps) {
  const { open, onClose, data, type, handleAction } = props

  const [
    formData,
    autoCompleteOptions,
    isLoadingOptions,
    errors,
    handleChange,
    handleSubmit,
    isValid,
  ] = useApproveConfirm(data, handleAction, type)

  return (
    <ModalContainer
      title="Confirm"
      titleInfo={
        <div className={Styles.title_info}>
          Load Type:<span className="orange-color"> {data.loadType}</span>
        </div>
      }
      open={open}
      onClose={onClose}
      styleName={Styles.approve_confirm_wrap}
      primaryButtonTitle="Submit"
      primaryButtonRightIcon={<ArrowForward />}
      onApply={handleSubmit}
      primaryButtonDisable={!isValid}
    >
      <div className="container">
        <div className="row">
          <>
            <div className="col-md-6 mb-3">
              <AutoComplete
                label="Customer Name"
                mandatory={true}
                placeHolder="Customer Name"
                value={formData.customer}
                error={errors?.customer}
                options={autoCompleteOptions.customers}
                onChange={(value) => handleChange('customer', value)}
                isLoading={isLoadingOptions.customers}
              />
            </div>
            <div className="col-md-6 mb-3">
              <AutoComplete
                label="Indent type"
                mandatory={true}
                placeHolder="Select indent type"
                onChange={(value) => handleChange('indentType', value)}
                options={autoCompleteOptions.indentTypes}
                value={formData.indentType}
                error={errors?.indentType}
                isLoading={isLoadingOptions.indentTypes}
              />
            </div>
            <div className="col-md-6 mb-3">
              <AutoComplete
                label="Origin"
                mandatory={true}
                placeHolder="Origin"
                value={formData.origin}
                error={errors?.origin}
                options={autoCompleteOptions.origins}
                onChange={(value) => handleChange('origin', value)}
                isLoading={isLoadingOptions.locations}
              />
            </div>

            <div className="col-md-6 mb-3">
              <AutoComplete
                label="Destination"
                mandatory={true}
                placeHolder="Destination"
                value={formData.destination}
                error={errors?.destination}
                options={autoCompleteOptions.destinations}
                onChange={(value) => handleChange('destination', value)}
              />
            </div>

            <div className="col-md-6 mb-3">
              <AutoComplete
                label="Contract ID"
                mandatory={true}
                placeHolder="Contract ID"
                value={formData.contractId}
                error={errors?.contractId}
                options={autoCompleteOptions.contractIds}
                onChange={(value) => handleChange('contractId', value)}
                isLoading={isLoadingOptions.contractIds}
                showCustomView={true}
                renderValueHolder={(data: any) => {
                  return (
                    <>
                      <span>{data.children}</span>
                    </>
                  )
                }}
                renderOption={(data: any) => {
                  return (
                    <Box display="flex" flexDirection="column">
                      <div>{data.label}</div>
                      <div>
                        {(data.data?.multipleStopage || []).map(
                          (stopage: any) => (
                            <Chips
                              key={stopage.sLocation?.id}
                              className="chips"
                              label={stopage.sLocation?.locationCity?.name}
                            />
                          )
                        )}
                      </div>
                    </Box>
                  )
                }}
              />
            </div>
            {typeof formData.contractId !== 'string' &&
              formData.contractId?.value && (
                <>
                  <NumberEditText
                    label="Tat"
                    placeholder="Tat"
                    value={formData.tat}
                    maxLength={10}
                    error={errors?.tat}
                    onChange={(value) => handleChange('tat', value)}
                    styleName="col-md-6 mb-3"
                    disabled
                    decimalScale={2}
                  />
                  <AutoComplete
                    mandatory
                    label="Vehicle Type"
                    placeHolder="Vehicle Type"
                    value={formData.vehicleType}
                    error={errors?.vehicleType}
                    onChange={(value) => handleChange('vehicleType', value)}
                    styleName="col-md-6 mb-3"
                    options={autoCompleteOptions.vehicleTypes}
                    isDisabled={!!formData.contractId}
                  />
                </>
              )}
          </>
        </div>
        <Accordion className="custom-accordion">
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls="panel1-content"
            id="panel1-header"
          >
            <Typography component="span">More Details</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <div className="row">
              <div className="col-md-6 mb-3">
                <EditText
                  label="Vehicle Number"
                  placeholder={'Vehicle Number'}
                  value={formData.vehicleNumber}
                  maxLength={10}
                  onChange={(value) => {
                    if (value && !ALPHANUMERIC_REGEX.test(value)) {
                      return
                    }
                    handleChange('vehicleNumber', value)
                  }}
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Rate"
                  placeholder={'Rate'}
                  mandatory={true}
                  error={errors?.rate}
                  value={formData.rate}
                  maxLength={10}
                  onChange={(value) => handleChange('rate', value)}
                />
              </div>
              <div className="col-md-6 mb-3">
                <DatePicker
                  includeTime
                  label="Placement Date&Time"
                  mandatory={true}
                  error={errors?.placementDateTime}
                  value={
                    formData.placementDateTime
                      ? dayjs(formData.placementDateTime)
                      : null
                  }
                  onChange={(date) =>
                    handleChange(
                      'placementDateTime',
                      convertDateTimeServerFormat(date ?? '')
                    )
                  }
                  icon={<CalendarMonth />}
                  iconPosition="end"
                  placeholder={'Placement Date&Time'}
                />
              </div>
              <div className="col-md-6 mb-3">
                <EditText
                  label="Driver Name"
                  placeholder={'Driver Name'}
                  value={formData.driverName}
                  maxLength={100}
                  onChange={(value) => {
                    if (value && !ALPHABETIC_WITH_SPACE_REGEX.test(value)) {
                      return
                    }
                    handleChange('driverName', value)
                  }}
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Driver Mobile Number"
                  placeholder={'Driver Mobile Number'}
                  value={formData.driverMobileNumber}
                  maxLength={10}
                  onChange={(value) =>
                    handleChange('driverMobileNumber', value)
                  }
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Buyer Mobile Number"
                  placeholder={'Buyer Mobile Number'}
                  value={formData.buyerMobileNumber}
                  maxLength={10}
                  onChange={(value) => handleChange('buyerMobileNumber', value)}
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Advance"
                  placeholder={'Advance'}
                  value={formData.advance}
                  maxLength={10}
                  onChange={(value) => handleChange('advance', value)}
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Balance"
                  placeholder={'Balance'}
                  value={formData.balance}
                  maxLength={15}
                  onChange={(value) => handleChange('balance', value)}
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Loading/Unloading Charges"
                  placeholder={'Loading/Unloading Charges'}
                  value={formData.loadingUnloadingCharges}
                  maxLength={15}
                  onChange={(value) =>
                    handleChange('loadingUnloadingCharges', value)
                  }
                />
              </div>
              <div className="col-md-6 mb-3">
                <NumberEditText
                  label="Margin"
                  placeholder={'Margin'}
                  value={formData.margin}
                  maxLength={15}
                  onChange={(value) => handleChange('margin', value)}
                />
              </div>
              <div className="col-md-6 mb-3">
                <EditText
                  label="Margin %"
                  placeholder={'Margin %'}
                  value={formData.marginPercent}
                  maxLength={10}
                  onChange={(value) => handleChange('marginPercent', value)}
                />
              </div>
              <div className="col-md-6 mb-3">
                <EditText
                  label="Calculation on extra loading"
                  placeholder={'Calculation on extra loading'}
                  value={formData.calculationOnExtraLoading}
                  maxLength={50}
                  onChange={(value) =>
                    handleChange('calculationOnExtraLoading', value)
                  }
                />
              </div>
              <div className="col-md-6">
                <TextArea
                  label="Remarks"
                  placeholder={'NA'}
                  value={formData.remarks}
                  maxLength={200}
                  onChange={(value) => handleChange('remarks', value)}
                />
              </div>
            </div>
          </AccordionDetails>
        </Accordion>
      </div>
    </ModalContainer>
  )
}

export default ApproveConfirmModal
