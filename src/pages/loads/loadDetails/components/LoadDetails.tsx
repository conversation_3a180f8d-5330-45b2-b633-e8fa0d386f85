import CardComponent from '@/components/cardComponent/CardComponent'
import { HighlightOff, ReportProblemSharp } from '@mui/icons-material'
import Styles from '@/pages/loads/loadDetails/components/LoadDetails.module.scss'
import { Skeleton } from '@mui/material'
import VendorAnalyticsModal from '@/pages/loads/loadDetails/components/VendorAnalyticsModal'
import { useAppDispatch } from '@/redux/store'
import {
  closeViewDocumentModal,
  openViewDocumentModal,
} from '@/redux/slices/AppSlice'
import {
  setModalData,
  setSelectedVendor,
} from '@/pages/loads/loadDetails/reducers/LoadDetails/LoadDetailsAction'
import { rejectTtile } from '@/constant/MessageUtils'
import { loadsListingAPIStatusEnum } from '@/constant/ArrayList'
import ConfirmationModal from '@/pages/loads/loadDetails/components/confirmationModal/ConfirmationModal'
import { useLoadDetails } from '@/pages/loads/loadDetails/hooks/useLoadDetails'
import VendorInfoSection from '@/pages/loads/loadDetails/components/VendorInfoSection'
import ShareDetailsWithVendorForm from '@/pages/loads/loadDetails/components/ShareDetailsWithVendorForm'
import LocationTimeline from '@/pages/loads/loadDetails/components/LocationTimeline'
import ActionButtons from '@/pages/loads/loadDetails/components/ActionButtons'
import TripSummary from '@/pages/loads/loadDetails/components/tripSummary/TripSummary'
import {
  ConfirmationModalContent,
  LoadDetailsActionType,
  LoadDetailsModal,
  LoadDetailsModalType,
} from '@/pages/loads/loadDetails/types/LoadDetailsModels'
import ViewDocumentModal from '@/components/modals/ViewDocumentModal/ViewDocumentModal'
import { useCallback, useMemo } from 'react'
import {
  isLoadApproved,
  VALID_CONFIRMATION_ACTIONS,
} from '@/pages/loads/loadDetails/utils'
import ApproveConfirmModal from './ApproveConfirmModal'
import { useNavigate } from 'react-router-dom'

type LoadDetailsProps = {
  loadId: string
}

const getConfirmationModalContent = (
  type: LoadDetailsActionType | undefined
): Partial<ConfirmationModalContent> => {
  const contentMap: Record<string, ConfirmationModalContent> = {
    REJECT_PENDING_LOAD: {
      heading: 'Reject',
      message: 'Are you sure you want to reject this load?',
      confirmButtonTitle: 'Yes, Reject it',
      icon: <HighlightOff />,
    },
    CREATE_INDENT: {
      heading: 'Create Indent',
      message: 'Do you want to proceed with creating an indent?',
      confirmButtonTitle: 'Yes',
      icon: <ReportProblemSharp className="orange-color" />,
    },
    MOVE_TO_LOAD_BOARD: {
      heading: 'Move to Load Board',
      message: 'Are you sure you want to move this load?',
      confirmButtonTitle: 'Yes, Move it',
      icon: <ReportProblemSharp className="orange-color" />,
    },
  }

  return contentMap[type || ''] || {}
}

const documents: any = {
  lr: [
    {
      documentLink:
        'https://images.unsplash.com/photo-1746937807433-05748b80caf4?q=80&w=3348&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      lrRecieptNo: 'LRN98739480',
    },
  ],
  eway: [
    {
      documentLink:
        'https://images.unsplash.com/photo-1746937807433-05748b80caf4?q=80&w=3348&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
  ],
  podFrontBack: [
    {
      documentLink:
        'https://images.unsplash.com/photo-1746937807433-05748b80caf4?q=80&w=3348&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      documentLink:
        'https://images.unsplash.com/photo-1746937807433-05748b80caf4?q=80&w=3348&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
  ],
  invoice: [{ documentLink: 'public/images/image.svg' }],
}

export const LoadDetials = (props: LoadDetailsProps) => {
  const { loadId } = props
  const appDispatch = useAppDispatch()
  const navigate = useNavigate()
  const { state, dispatch, handleAction, viewDocumentModalState } =
    useLoadDetails(loadId)

  const {
    modalState,
    loading,
    data,
    actionButtonLoader,
    vendorAnalyticsData,
    selectedVendor,
  } = state

  const status = data?.status || loadsListingAPIStatusEnum.PENDING
  const isApproved = isLoadApproved(status)

  const showConfirmationModal =
    modalState?.isOpen &&
    modalState?.isConfirmationModal &&
    data?.loadType.toUpperCase() === 'REGULAR'
  const showApprovalModal =
    modalState?.isOpen &&
    modalState?.isApprovalModal &&
    data?.loadType.toUpperCase() === 'NEW'
  const confirmationModalContent = getConfirmationModalContent(
    modalState?.type as LoadDetailsActionType
  )

  const locations = useMemo(
    () => ({
      origin: data?.originName || '',
      wayPoints: data?.waypoints || [],
      destination: data?.destinationName || '',
    }),
    [data?.originName, data?.waypoints, data?.destinationName]
  )

  const handleCloseConfirmationModal = useCallback(() => {
    if (modalState?.type) {
      dispatch(setModalData(modalState.type, false))
    }
  }, [dispatch, modalState?.type])

  const handleConfirmAction = useCallback(() => {
    if (
      modalState?.type &&
      VALID_CONFIRMATION_ACTIONS.includes(
        modalState.type as LoadDetailsActionType
      )
    ) {
      if (modalState.type === LoadDetailsModal.CREATE_INDENT) {
        // handleAction(modalState.type as LoadDetailsActionType, state.data)
        navigate('/approved')
        handleCloseConfirmationModal()
      }
      // handleAction(modalState.type as LoadDetailsActionType)
      navigate('/approved')
      handleCloseConfirmationModal()
    }
  }, [handleCloseConfirmationModal, modalState?.type, navigate])

  const handleOpenVendorAnalyticsModal = useCallback(() => {
    dispatch(setModalData('VENDOR_ANALYTICS', true))
  }, [dispatch])

  const handleDocumentOpen = useCallback(
    (key: string, title: string) => {
      appDispatch(openViewDocumentModal({ links: documents[key], title }))
    },
    [appDispatch]
  )

  const handleVendorSelect = useCallback(
    (vendor: any) => {
      dispatch(setSelectedVendor(vendor))
    },
    [dispatch]
  )

  if (loading) {
    return (
      <div className={Styles.load_details}>
        <Skeleton variant="rectangular" width={400} height={600} />
      </div>
    )
  }

  return (
    <div className={Styles.load_details}>
      {viewDocumentModalState.isOpen && (
        <ViewDocumentModal
          open
          onClose={() => appDispatch(closeViewDocumentModal())}
          fileLinks={viewDocumentModalState.fileLinks}
          title={viewDocumentModalState.title}
        />
      )}
      {modalState?.isOpen && modalState?.type === 'VENDOR_ANALYTICS' && (
        <VendorAnalyticsModal
          open
          onClose={() => {
            dispatch(setModalData(LoadDetailsModal.VENDOR_ANALYTICS, false))
          }}
          analyticsData={vendorAnalyticsData}
          selectedVendor={selectedVendor}
          onVendorSelect={handleVendorSelect}
        />
      )}

      {showConfirmationModal && (
        <ConfirmationModal
          open
          onClose={handleCloseConfirmationModal}
          onConfirm={handleConfirmAction}
          heading={confirmationModalContent.heading || ''}
          message={confirmationModalContent.message || ''}
          confirmButtonTitle={confirmationModalContent.confirmButtonTitle || ''}
          icon={confirmationModalContent.icon}
        />
      )}
      {showApprovalModal && (
        <ApproveConfirmModal
          open={modalState?.isOpen || false}
          data={data}
          onClose={() => {
            dispatch(setModalData(LoadDetailsModal.CREATE_INDENT, false))
          }}
          type={state?.modalState?.type as LoadDetailsModalType}
          handleAction={handleAction}
        />
      )}

      {/* {status.toUpperCase() === loadsListingAPIStatusEnum.APPROVED && (
        <Button
          className={'btn-warning w-100'}
          title={'Create Indent'}
          radius={'radius-none'}
          leftIcon={<OpenWith />}
        />
      )} */}

      {/* Location Timeline */}
      <CardComponent styleName={Styles.load_Container}>
        <LocationTimeline
          origin={locations.origin || ''}
          destination={locations.destination || ''}
          waypoints={locations.wayPoints.length ? locations.wayPoints : []}
        />

        <ActionButtons
          status={status}
          dispatch={dispatch}
          handleAction={handleAction}
          actionButtonLoader={actionButtonLoader}
        />
      </CardComponent>

      {/* Share Details with Vendor */}
      {isApproved && (
        <ShareDetailsWithVendorForm
          handleOpenVendorAnalytics={handleOpenVendorAnalyticsModal}
          vendorAnalyticsData={vendorAnalyticsData}
          selectedVendor={selectedVendor}
          onVendorSelect={handleVendorSelect}
        />
      )}

      {/* Vendor Info */}
      {isApproved && (
        <VendorInfoSection rejectTitle={rejectTtile} loadDetailsData={state} />
      )}

      {/* Trip Summary */}
      <TripSummary state={state} handleDocumentOpen={handleDocumentOpen} />
    </div>
  )
}
