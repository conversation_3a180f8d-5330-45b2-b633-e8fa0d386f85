import { isNullValue } from '@/utils/StringUtils'
import { SchemaWithValidators, ValidationRule } from '@/utils/ValidationUtils'
import {
  CreateLoadFormDataType,
  CreateNewLoadFormDataType,
  CreateRegularLoadFormDataType,
} from '@/pages/createLoad/types/CreateLoadModel'
import { MOBILE_NUMBER_REGEX } from '@/constant/ValidationConstants'

export const createLoadSchema: SchemaWithValidators = {
  schema: {
    loadType: [{ type: 'loadType', msg: 'Load type is required' }],
    indentType: [{ type: 'indentType', msg: 'Indent type is required' }],
    customer: [{ type: 'customer', msg: 'Customer is required' }],
    origin: [{ type: 'origin', msg: 'Origin is required' }],
    destination: [{ type: 'destination', msg: 'Destination is required' }],
    contractId: [{ type: 'contractId', msg: 'Contract ID is required' }],
    tat: [{ type: 'tat', msg: 'TAT is required' }],
    placementDateTime: [
      { type: 'placementDateTime', msg: 'Placement date and time is required' },
    ],
    vehicleType: [{ type: 'vehicleType', msg: 'Vehicle type is required' }],
    rate: [{ type: 'rate', msg: 'Rate is required' }],
    driverMobileNumber: [
      {
        type: 'driverMobileNumber',
        msg: 'Please enter a valid 10-digit number',
      },
    ],
    buyerMobileNumber: [
      {
        type: 'buyerMobileNumber',
        msg: 'Please enter a valid 10-digit number',
      },
    ],
  },
  validators: {
    loadType: (value: string) => ({ isValid: !isNullValue(value) }),
    indentType: (
      value: string,
      _rule?: ValidationRule,
      formData?: CreateLoadFormDataType
    ) => {
      const isValid = !isLoadTypeRegular(formData!) || !isNullValue(value)
      return { isValid }
    },
    customer: (value: string) => ({ isValid: !isNullValue(value) }),
    origin: (value: string) => ({ isValid: !isNullValue(value) }),
    destination: (value: string) => ({ isValid: !isNullValue(value) }),
    contractId: (
      value: string,
      _rule?: ValidationRule,
      formData?: CreateLoadFormDataType
    ) => {
      const isValid = !isLoadTypeRegular(formData!) || !isNullValue(value)
      return { isValid }
    },
    tat: (
      value: string,
      _rule?: ValidationRule,
      formData?: CreateLoadFormDataType
    ) => ({ isValid: !isLoadTypeNew(formData!) || !isNullValue(value) }),
    placementDateTime: (value: string) => ({ isValid: !isNullValue(value) }),
    vehicleType: (value: string) => ({ isValid: !isNullValue(value) }),
    rate: (value: string) => ({ isValid: !isNullValue(value) }),
    driverMobileNumber: (value: string) => ({
      isValid: isNullValue(value) || isValidMobileNumber(value),
    }),
    buyerMobileNumber: (value: string) => ({
      isValid: isNullValue(value) || isValidMobileNumber(value),
    }),
  },
}

export const createIntendSchema: SchemaWithValidators = {
  schema: {
    loadType: [{ type: 'loadType', msg: 'Load type is required' }],
    indentType: [{ type: 'indentType', msg: 'Indent type is required' }],
    customer: [{ type: 'customer', msg: 'Customer is required' }],
    origin: [{ type: 'origin', msg: 'Origin is required' }],
    destination: [{ type: 'destination', msg: 'Destination is required' }],
    contractId: [{ type: 'contractId', msg: 'Contract ID is required' }],
    placementDateTime: [
      { type: 'placementDateTime', msg: 'Placement date and time is required' },
    ],
    vehicleType: [{ type: 'vehicleType', msg: 'Vehicle type is required' }],
    rate: [{ type: 'rate', msg: 'Rate is required' }],
  },
  validators: {
    loadType: (value: string) => ({ isValid: !isNullValue(value) }),
    indentType: (value: string) => ({ isValid: !isNullValue(value) }),
    customer: (value: string) => ({ isValid: !isNullValue(value) }),
    origin: (value: string) => ({ isValid: !isNullValue(value) }),
    destination: (value: string) => ({ isValid: !isNullValue(value) }),
    contractId: (value: string) => ({ isValid: !isNullValue(value) }),
    placementDateTime: (value: string) => ({ isValid: !isNullValue(value) }),
    vehicleType: (value: string) => ({ isValid: !isNullValue(value) }),
    rate: (value: string) => ({ isValid: !isNullValue(value) }),
  },
}

export const isLoadTypeRegular = (
  formData: CreateLoadFormDataType
): formData is CreateRegularLoadFormDataType => {
  return formData.loadType?.label === 'REGULAR'
}

export const isLoadTypeNew = (
  formData: CreateLoadFormDataType
): formData is CreateNewLoadFormDataType => {
  return formData.loadType?.label === 'NEW'
}

export const isValidMobileNumber = (value: string) => {
  return MOBILE_NUMBER_REGEX.test(value)
}
