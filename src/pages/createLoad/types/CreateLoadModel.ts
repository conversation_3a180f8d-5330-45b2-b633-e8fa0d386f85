import { OptionType } from '@/components/widgets/widgetsInterfaces'

export type LoadingAutoCompleteOptionsType = {
  loadTypes: boolean
  vehicleTypes: boolean
  indentTypes: boolean
  customers: boolean
  locations: boolean
  contractIds: boolean
}

export type CreateLoadState = {
  formData: CreateLoadFormDataType
  autoCompleteOptions: CreateLoadAutoCompleteOptionsType
  isLoadingOptions: LoadingAutoCompleteOptionsType
  errors: Partial<Record<keyof CreateLoadFormDataType, string | undefined>>
  isPending: boolean
  isSubmitting: boolean
}

type CreateLoadFormCommonDataType = {
  driverName: string
  driverMobileNumber: string
  buyerMobileNumber: string
  placementDateTime: string
  vehicleType: OptionType | undefined
  vehicleNumber: string
  vehicleRefrenceId: string
  rate: number | undefined
  advance: number | undefined
  balance: number | undefined
  loadingUnloadingCharges: number | undefined
  margin: number | undefined
  marginPercent: number | undefined
  calculationOnExtraLoading: string
  remarks: string
  tat: number | undefined
  waypoints: { label: string; value: string }[]
}

export type CreateRegularLoadFormDataType = CreateLoadFormCommonDataType & {
  loadType: OptionType & { label: 'REGULAR' }
  indentType: OptionType | undefined
  customer: OptionType | undefined
  origin: OptionType | undefined
  destination: OptionType | undefined
  contractId: OptionType | undefined
}

export type CreateNewLoadFormDataType = CreateLoadFormCommonDataType & {
  loadType: OptionType & { label: 'NEW' }
  indentType: OptionType | undefined
  customer: string | undefined
  origin: string | undefined
  destination: string | undefined
  contractId: string | undefined
}

type InitialCreateLoadFormDataType = CreateLoadFormCommonDataType & {
  loadType: undefined
  indentType: OptionType | undefined
  customer: undefined
  origin: undefined
  destination: undefined
  contractId: undefined
  waypoints: []
}

export type CreateLoadFormDataType =
  | InitialCreateLoadFormDataType
  | CreateNewLoadFormDataType
  | CreateRegularLoadFormDataType

export const INITIAL_FORM_DATA: InitialCreateLoadFormDataType = {
  loadType: undefined,
  indentType: undefined,
  customer: undefined,
  origin: undefined,
  destination: undefined,
  contractId: undefined,
  driverName: '',
  driverMobileNumber: '',
  buyerMobileNumber: '',
  placementDateTime: '',
  vehicleType: undefined,
  vehicleNumber: '',
  vehicleRefrenceId: '',
  rate: undefined,
  advance: undefined,
  balance: undefined,
  loadingUnloadingCharges: undefined,
  margin: undefined,
  marginPercent: undefined,
  calculationOnExtraLoading: '',
  remarks: '',
  tat: undefined,
  waypoints: [],
}

export type CreateLoadAutoCompleteOptionsType = {
  loadTypes: OptionType[]
  indentTypes: OptionType[]
  customers: OptionType[]
  origins: OptionType[]
  destinations: OptionType[]
  contractIds: OptionType[]
  vehicleTypes: OptionType[]
}

export const INITIAL_AUTO_COMPLETE_OPTIONS = {
  loadTypes: [],
  indentTypes: [],
  customers: [],
  origins: [],
  destinations: [],
  contractIds: [],
  vehicleTypes: [],
}
