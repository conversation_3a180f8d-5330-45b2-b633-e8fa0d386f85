import CreateLoadActionTypes from '@/pages/createLoad/reducers/CreateLoadActionTypes'
import {
  CreateLoadAutoCompleteOptionsType,
  CreateLoadFormDataType,
  CreateLoadState,
  LoadingAutoCompleteOptionsType,
} from '@/pages/createLoad/types/CreateLoadModel'
import { OptionType } from '@/components/widgets/widgetsInterfaces'

export const setFormData = (
  field: keyof CreateLoadFormDataType,
  value: unknown
) => ({
  type: CreateLoadActionTypes.SET_FORM_DATA,
  field,
  value,
})

export const setAutoCompleteOptions = (
  optionType: keyof CreateLoadAutoCompleteOptionsType,
  value: OptionType[]
) => ({
  type: CreateLoadActionTypes.SET_AUTO_COMPLETE_OPTIONS,
  optionType,
  value,
})

export const setIsLoadingOptions = (
  loadingOptionsTypes: (keyof LoadingAutoCompleteOptionsType)[],
  isLoading: boolean
) => ({
  type: CreateLoadActionTypes.SET_IS_LOADING_OPTIONS,
  loadingOptionsTypes,
  isLoading,
})

export const setValue = (value: any, valueType: keyof CreateLoadState) => ({
  type: CreateLoadActionTypes.SET_VALUE,
  value,
  valueType,
})

export const setErrors = (
  errors: Partial<Record<keyof CreateLoadFormDataType, string | undefined>>
) => ({
  type: CreateLoadActionTypes.SET_ERRORS,
  errors,
})

export const setIsPending = (value: any) => ({
  type: CreateLoadActionTypes.SET_IS_PENDING,
  value,
})

export const setIsSubmitting = (value: any) => ({
  type: CreateLoadActionTypes.SET_IS_SUBMITTING,
  value,
})
