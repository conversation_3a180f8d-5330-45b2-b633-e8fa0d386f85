import { useNavigate } from 'react-router-dom'
import CardComponent from '@/components/cardComponent/CardComponent'
import Filter from '@/components/filter/Filter'
import PageContainer from '@/components/pageContainer/PageContainer'
import AutoComplete from '@/components/widgets/autoComplete/AutoComplete'
import Button from '@/components/widgets/button/Button'
import DatePicker from '@/components/widgets/datePicker/DatePicker'
import EditText from '@/components/widgets/editText/EditText'
import TextArea from '@/components/widgets/textArea/TextArea'
import {
  advanceLabel,
  balanceLabel,
  buyerMobileNumberLabel,
  calculationOnExtraLoadingLabel,
  contractIdTitle,
  createLoadTitle,
  customerNameLabel,
  destinationLabel,
  driverMobileNumberLabel,
  driverNameTitle,
  indentTypeTitle,
  loadingUnloadingChargesLabel,
  loadTypeTitle,
  marginLabel,
  marginPercentLabel,
  originLabel,
  placementDateandTimeLabel,
  rateLabel,
  remarksLabel,
  selectContractIdTitle,
  selectCustomerNameTitle,
  selectDestinationTitle,
  selectLoadTypeTitle,
  selectOriginTitle,
  submitTitle,
  tatTitle,
  vehicleNumberLabel,
  vehicleRefrenceIdTitle,
  vehicleTypeLabel,
  wayPointsLabel,
} from '@/constant/MessageUtils'
import { isMobile } from '@/utils/ViewUtils'
import { ArrowBack, ArrowForward, CalendarMonth } from '@mui/icons-material'
import useCreateLoad from '@/pages/createLoad/hooks/useCreateLoad'
import NumberEditText from '@/components/widgets/NumberEditText'
import dayjs from 'dayjs'
import { convertDateTimeServerFormat } from '@/utils/DateUtils'
import Chips from '@/components/chips/Chips'
import { isLoadTypeNew, isLoadTypeRegular } from '@/pages/createLoad/utils'
import {
  ALPHABETIC_WITH_SPACE_REGEX,
  ALPHANUMERIC_REGEX,
} from '@/constant/ValidationConstants'
import { Box } from '@mui/material'

const CreateLoad = () => {
  const navigate = useNavigate()

  const [
    formData,
    autoCompleteOptions,
    isLoadingOptions,
    errors,
    handleChange,
    isPending,
    handleSubmit,
  ] = useCreateLoad()

  return (
    <PageContainer>
      <Filter
        pageTitle={createLoadTitle}
        buttonTitle={isMobile ? '' : 'Back'}
        leftIcon={<ArrowBack />}
        classNameBtn="btn-white"
        onClick={() => navigate(-1)}
      />
      <CardComponent
        action={
          <>
            <Button
              title={submitTitle}
              className={'btn-blue'}
              rightIcon={<ArrowForward />}
              onClick={handleSubmit}
              loading={isPending}
            />
          </>
        }
      >
        <div className="row">
          {/* Load and Indent Types */}
          <div className="mb-3 col-md-4">
            <AutoComplete
              label={loadTypeTitle}
              mandatory={true}
              placeHolder={selectLoadTypeTitle}
              onChange={(value) => handleChange('loadType', value)}
              options={autoCompleteOptions.loadTypes}
              value={formData.loadType}
              error={errors?.loadType}
              isLoading={isLoadingOptions.loadTypes}
            />
          </div>
          {isLoadTypeRegular(formData) && (
            <>
              <div className="mb-3 col-md-4">
                <AutoComplete
                  label={indentTypeTitle}
                  mandatory={true}
                  placeHolder="Select indent type"
                  onChange={(value) => handleChange('indentType', value)}
                  options={autoCompleteOptions.indentTypes}
                  value={formData.indentType}
                  error={errors?.indentType}
                  isLoading={isLoadingOptions.indentTypes}
                />
              </div>
              <div className="mb-3 col-md-4">
                <AutoComplete
                  label={customerNameLabel}
                  mandatory={true}
                  placeHolder={selectCustomerNameTitle}
                  value={formData.customer}
                  error={errors?.customer}
                  options={autoCompleteOptions.customers}
                  onChange={(value) => handleChange('customer', value)}
                  isLoading={isLoadingOptions.customers}
                />
              </div>
              <div className="mb-3 col-md-4">
                <AutoComplete
                  label={originLabel}
                  mandatory={true}
                  placeHolder={selectOriginTitle}
                  value={formData.origin}
                  error={errors?.origin}
                  options={autoCompleteOptions.origins}
                  onChange={(value) => handleChange('origin', value)}
                  isLoading={isLoadingOptions.locations}
                />
              </div>

              <div className="mb-3 col-md-4">
                <AutoComplete
                  label={destinationLabel}
                  mandatory={true}
                  placeHolder={selectDestinationTitle}
                  value={formData.destination}
                  error={errors?.destination}
                  options={autoCompleteOptions.destinations}
                  onChange={(value) => handleChange('destination', value)}
                />
              </div>

              <div className="mb-3 col-md-4">
                <AutoComplete
                  label={contractIdTitle}
                  mandatory={true}
                  placeHolder={selectContractIdTitle}
                  value={formData.contractId}
                  error={errors?.contractId}
                  options={autoCompleteOptions.contractIds}
                  onChange={(value) => handleChange('contractId', value)}
                  isLoading={isLoadingOptions.contractIds}
                  showCustomView={true}
                  renderValueHolder={(data: any) => {
                    return (
                      <>
                        <span>{data.children}</span>
                      </>
                    )
                  }}
                  renderOption={(data: any) => {
                    return (
                      <Box display="flex" flexDirection="column">
                        <div>{data.label}</div>
                        <div>
                          {(data.data?.multipleStopage || []).map(
                            (stopage: any) => (
                              <Chips
                                key={stopage.sLocation?.id}
                                className="chips"
                                label={stopage.sLocation?.locationCity?.name}
                              />
                            )
                          )}
                        </div>
                      </Box>
                    )
                  }}
                />
              </div>
              {formData.contractId?.value && (
                <>
                  <div className="mb-3 col-md-4">
                    <AutoComplete
                      label={wayPointsLabel}
                      placeHolder={''}
                      value={formData.waypoints}
                      error={''}
                      isDisabled
                      options={undefined}
                      onChange={() => {}}
                      isMulti={true}
                    />
                  </div>
                  <NumberEditText
                    label={tatTitle}
                    placeholder={tatTitle}
                    value={formData.tat}
                    maxLength={10}
                    error={errors?.tat}
                    onChange={(value) => handleChange('tat', value)}
                    styleName="mb-3 col-md-4"
                    disabled
                    decimalScale={2}
                  />
                </>
              )}
            </>
          )}
          {isLoadTypeNew(formData) && (
            <>
              <EditText
                mandatory
                label={customerNameLabel}
                styleName="mb-3 col-md-4"
                placeholder={customerNameLabel}
                value={formData.customer}
                error={errors?.customer}
                maxLength={50}
                onChange={(value) => handleChange('customer', value)}
              />
              <EditText
                mandatory
                label={originLabel}
                styleName="mb-3 col-md-4"
                placeholder={originLabel}
                value={formData.origin ?? undefined}
                error={errors?.origin}
                maxLength={50}
                onChange={(value) => handleChange('origin', value)}
              />
              <EditText
                mandatory
                label={destinationLabel}
                styleName="mb-3 col-md-4"
                placeholder={destinationLabel}
                value={formData.destination ?? undefined}
                error={errors?.destination}
                maxLength={50}
                onChange={(value) => handleChange('destination', value)}
              />
              <EditText
                mandatory
                label={tatTitle}
                styleName="mb-3 col-md-4"
                placeholder={tatTitle}
                value={formData.tat ?? undefined}
                error={errors?.tat}
                maxLength={50}
                onChange={(value) => handleChange('tat', value)}
              />
            </>
          )}

          <div className="mb-3 col-md-4">
            <AutoComplete
              mandatory
              label={vehicleTypeLabel}
              placeHolder={vehicleTypeLabel}
              value={formData.vehicleType}
              error={errors?.vehicleType}
              onChange={(value) => handleChange('vehicleType', value)}
              options={autoCompleteOptions.vehicleTypes}
              isDisabled={isLoadTypeRegular(formData) && !!formData.contractId}
            />
          </div>

          <EditText
            label={vehicleNumberLabel}
            styleName="mb-3 col-md-4"
            placeholder={vehicleNumberLabel}
            value={formData.vehicleNumber}
            maxLength={11}
            onChange={(value) => {
              if (value && !ALPHANUMERIC_REGEX.test(value)) {
                return
              }
              handleChange('vehicleNumber', value)
            }}
            error={errors?.vehicleNumber}
          />

          <EditText
            label={vehicleRefrenceIdTitle}
            styleName="mb-3 col-md-4"
            placeholder={vehicleRefrenceIdTitle}
            value={formData.vehicleRefrenceId}
            maxLength={20}
            onChange={(value) => handleChange('vehicleRefrenceId', value)}
            error={errors?.vehicleRefrenceId}
          />

          {/* Placement and Vehicle info */}
          <DatePicker
            includeTime
            mandatory
            label={placementDateandTimeLabel}
            placeholder={placementDateandTimeLabel}
            value={
              formData.placementDateTime
                ? dayjs(formData.placementDateTime)
                : null
            }
            onChange={(date) =>
              handleChange(
                'placementDateTime',
                convertDateTimeServerFormat(date ?? '')
              )
            }
            icon={<CalendarMonth />}
            iconPosition="end"
            styleName="mb-3 col-md-4"
            error={errors?.placementDateTime}
          />

          <NumberEditText
            label={rateLabel}
            styleName="mb-3 col-md-4"
            placeholder={rateLabel}
            mandatory={true}
            value={formData.rate}
            maxLength={15}
            onChange={(value) => handleChange('rate', value)}
            error={errors?.rate}
          />

          <EditText
            label={driverNameTitle}
            styleName="mb-3 col-md-4"
            placeholder={driverNameTitle}
            value={formData.driverName}
            maxLength={100}
            onChange={(value) => {
              if (value && !ALPHABETIC_WITH_SPACE_REGEX.test(value)) {
                return
              }
              handleChange('driverName', value)
            }}
            error={errors?.driverName}
          />

          {/* Contact information */}
          <NumberEditText
            label={driverMobileNumberLabel}
            styleName="mb-3 col-md-4"
            placeholder={driverMobileNumberLabel}
            value={formData.driverMobileNumber}
            maxLength={10}
            onChange={(value) => handleChange('driverMobileNumber', value)}
            error={errors?.driverMobileNumber}
            decimalSeparator={false}
          />

          <NumberEditText
            label={buyerMobileNumberLabel}
            styleName="mb-3 col-md-4"
            placeholder={buyerMobileNumberLabel}
            value={formData.buyerMobileNumber}
            maxLength={10}
            onChange={(value) => handleChange('buyerMobileNumber', value)}
            error={errors?.buyerMobileNumber}
            decimalSeparator={false}
          />

          <NumberEditText
            label={advanceLabel}
            styleName="mb-3 col-md-4"
            placeholder={advanceLabel}
            value={formData.advance}
            maxLength={15}
            onChange={(value) => handleChange('advance', value)}
            error={errors?.advance}
          />

          <NumberEditText
            label={balanceLabel}
            styleName="mb-3 col-md-4"
            placeholder={balanceLabel}
            value={formData.balance}
            maxLength={15}
            onChange={(value) => handleChange('balance', value)}
            error={errors?.balance}
          />

          <NumberEditText
            label={loadingUnloadingChargesLabel}
            styleName="mb-3 col-md-4"
            placeholder={loadingUnloadingChargesLabel}
            value={formData.loadingUnloadingCharges}
            maxLength={15}
            onChange={(value) => handleChange('loadingUnloadingCharges', value)}
            error={errors?.loadingUnloadingCharges}
          />

          <NumberEditText
            label={marginLabel}
            styleName="mb-3 col-md-4"
            placeholder={marginLabel}
            value={formData.margin}
            maxLength={15}
            onChange={(value) => handleChange('margin', value)}
            error={errors?.margin}
          />

          <NumberEditText
            label={marginPercentLabel}
            styleName="mb-3 col-md-4"
            placeholder={marginPercentLabel}
            value={formData.marginPercent}
            maxLength={10}
            onChange={(value) => handleChange('marginPercent', value)}
            error={errors?.marginPercent}
          />

          <EditText
            label={calculationOnExtraLoadingLabel}
            styleName="mb-3 col-md-4"
            placeholder={calculationOnExtraLoadingLabel}
            value={formData.calculationOnExtraLoading}
            maxLength={50}
            onChange={(value) =>
              handleChange('calculationOnExtraLoading', value)
            }
            error={errors?.calculationOnExtraLoading}
          />

          <TextArea
            label={remarksLabel}
            placeholder={remarksLabel}
            styleName={'mb-3 col-md-4'}
            value={formData.remarks}
            maxLength={200}
            onChange={(value) => handleChange('remarks', value)}
            maxRows={3}
          />
        </div>
      </CardComponent>
    </PageContainer>
  )
}

export default CreateLoad
