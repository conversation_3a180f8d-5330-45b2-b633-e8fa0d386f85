import { Dispatch, useEffect } from 'react'
import {
  CreateLoadAutoCompleteOptionsType,
  CreateLoadFormDataType,
} from '@/pages/createLoad/types/CreateLoadModel'
import {
  setAutoCompleteOptions,
  setIsLoadingOptions,
} from '@/pages/createLoad/reducers/CreateLoadActions'
import {
  setAutoCompleteList,
  setAutoCompleteListWithData,
  setAutoCompleteListWithLabelConvertorAndValue,
} from '@/utils/DataUtils'
import { isLoadTypeRegular } from '../utils'
import { createFormattedOriginsOptions } from '@/utils/SharedUtils'

interface UseAutoCompleteEffectsProps {
  formData: CreateLoadFormDataType
  autoCompleteOptions: CreateLoadAutoCompleteOptionsType
  dispatch: Dispatch<any>
  fetchers: {
    fetchAllLoadTypes: () => Promise<any>
    fetchVehicleTypes: () => Promise<any>
    fetchIndentTypes: () => Promise<any>
    fetchCustomers: () => Promise<any>
    fetchLocations: (args: any) => Promise<any>
    fetchContractIds: (args: any) => Promise<any>
  }
  onNew?: boolean
}

export const useAutoCompleteEffects = ({
  formData,
  autoCompleteOptions,
  dispatch,
  fetchers,
  onNew,
}: UseAutoCompleteEffectsProps) => {
  const {
    fetchAllLoadTypes,
    fetchVehicleTypes,
    fetchIndentTypes,
    fetchCustomers,
    fetchLocations,
    fetchContractIds,
  } = fetchers

  useEffect(() => {
    const fetchRequiredAutocompleteOptions = async () => {
      dispatch(setIsLoadingOptions(['loadTypes', 'vehicleTypes'], true))
      try {
        await Promise.all([fetchAllLoadTypes(), fetchVehicleTypes()])
      } catch (error) {
        console.error('Error fetching auto-complete options:', error)
      } finally {
        dispatch(setIsLoadingOptions(['loadTypes', 'vehicleTypes'], false))
      }
    }

    if (
      !autoCompleteOptions.loadTypes.length &&
      !autoCompleteOptions.vehicleTypes.length
    ) {
      fetchRequiredAutocompleteOptions()
    }
  }, [
    dispatch,
    fetchAllLoadTypes,
    fetchVehicleTypes,
    autoCompleteOptions.loadTypes.length,
    autoCompleteOptions.vehicleTypes.length,
  ])

  useEffect(() => {
    const fetchDependentAutoCompleteOptions = async () => {
      try {
        const fetchPromises: Array<{ promise: Promise<any>; type: string }> = []

        if (!autoCompleteOptions.indentTypes.length) {
          fetchPromises.push({
            promise: fetchIndentTypes(),
            type: 'indentTypes',
          })
          dispatch(setIsLoadingOptions(['indentTypes'], true))
        }

        if (!autoCompleteOptions.customers.length) {
          fetchPromises.push({
            promise: fetchCustomers(),
            type: 'customers',
          })
          dispatch(setIsLoadingOptions(['customers'], true))
        }

        if (
          (isRegularLoad || onNew) &&
          typeof formData.customer !== 'string' &&
          formData.customer?.data?.id !== undefined &&
          !autoCompleteOptions.origins.length
        ) {
          fetchPromises.push({
            promise: fetchLocations({ id: Number(formData.customer.data.id) }),
            type: 'origins',
          })
          dispatch(setIsLoadingOptions(['locations'], true))
        }

        if (
          (isRegularLoad || onNew) &&
          typeof formData.customer !== 'string' &&
          formData.customer?.data?.id !== undefined &&
          typeof formData.origin !== 'string' &&
          formData.origin?.value !== undefined &&
          typeof formData.destination !== 'string' &&
          formData.destination?.value !== undefined &&
          !autoCompleteOptions.contractIds.length
        ) {
          fetchPromises.push({
            promise: fetchContractIds({
              customer_id: formData.customer.data.id,
              start_point: formData.origin.data?.locationName,
              destination_point: formData.destination.data?.locationName,
            }),
            type: 'contractIds',
          })
          dispatch(setIsLoadingOptions(['contractIds'], true))
        }

        if (fetchPromises.length > 0) {
          const results = await Promise.allSettled(
            fetchPromises.map((item) => item.promise)
          )

          results.forEach((result, index) => {
            const { type } = fetchPromises[index]

            if (result.status === 'rejected') {
              console.error(`Error fetching ${type}:`, result.reason)
              return
            }

            const data = result.value

            if (!Array.isArray(data)) {
              console.warn(`Expected array for ${type}, got:`, typeof result)
              return
            }

            switch (type) {
              case 'indentTypes':
                dispatch(
                  setAutoCompleteOptions(
                    'indentTypes',
                    setAutoCompleteList(data, 'name', 'id')
                  )
                )
                break

              case 'customers':
                dispatch(
                  setAutoCompleteOptions(
                    'customers',
                    setAutoCompleteListWithLabelConvertorAndValue(
                      data,
                      (customer) =>
                        `${customer.companyName}<${customer.customerCode}>`,
                      'id'
                    )
                  )
                )
                break

              case 'origins': {
                const formattedOrigins = createFormattedOriginsOptions(
                  data || []
                )
                dispatch(setAutoCompleteOptions('origins', formattedOrigins))
                break
              }
              case 'contractIds':
                dispatch(
                  setAutoCompleteOptions(
                    'contractIds',
                    setAutoCompleteListWithData(
                      data,
                      'contractId',
                      'contractId'
                    )
                  )
                )
                break

              default:
                console.warn(`Unknown result type: ${type}`)
            }
          })
        }
      } catch (error) {
        console.error('Error fetching dependent auto-complete options:', error)
      } finally {
        dispatch(
          setIsLoadingOptions(
            ['indentTypes', 'customers', 'locations', 'contractIds'],
            false
          )
        )
      }
    }

    const isRegularLoad =
      isLoadTypeRegular(formData) && (onNew === false || onNew === undefined)

    if (isRegularLoad || onNew) {
      fetchDependentAutoCompleteOptions()
    }
  }, [
    formData,
    autoCompleteOptions.indentTypes.length,
    autoCompleteOptions.customers.length,
    autoCompleteOptions.origins.length,
    autoCompleteOptions.contractIds.length,
    fetchIndentTypes,
    fetchCustomers,
    fetchLocations,
    fetchContractIds,
    dispatch,
    onNew,
  ])
}
