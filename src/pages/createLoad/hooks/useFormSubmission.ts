import { createLoad } from '@/api/serviceActions/CreateLoadServiceActions'
import { useAsyncThunk } from '@/hooks/useAsyncThunk'
import { useTransition } from 'react'
import { CreateLoadFormDataType } from '@/pages/createLoad/types/CreateLoadModel'
import { isLoadTypeRegular } from '@/pages/createLoad/utils'
import { useAppDispatch } from '@/redux/store'
import { showSuccessAlert } from '@/redux/slices/AppSlice'
import { useNavigate } from 'react-router-dom'
import { home } from '@/constant/RoutePath'

export const useFormSubmission = (formData: CreateLoadFormDataType) => {
  const appDispatch = useAppDispatch()
  const navigate = useNavigate()

  const [isPending, startTransition] = useTransition()
  const { execute: createLoadBoard } = useAsyncThunk(createLoad, {
    immediate: false,
  })

  const createSubmissionPayload = (formData: CreateLoadFormDataType) => ({
    loadType: formData.loadType?.label,
    indentType: formData.indentType?.label,
    customerId: isLoadTypeRegular(formData)
      ? formData.customer?.value?.toString()
      : undefined,
    customerName: isLoadTypeRegular(formData)
      ? formData.customer?.data?.companyName
      : formData.customer,
    customerCode: isLoadTypeRegular(formData)
      ? formData.customer?.data?.customerCode
      : undefined,
    originId: isLoadTypeRegular(formData)
      ? formData.origin?.data?.id
      : undefined,
    originName: isLoadTypeRegular(formData)
      ? formData.origin?.data?.locationName
      : formData.origin,
    destinationId: isLoadTypeRegular(formData)
      ? formData.destination?.data?.id
      : undefined,
    destinationName: isLoadTypeRegular(formData)
      ? formData.destination?.data?.locationName
      : formData.destination,
    contractId: isLoadTypeRegular(formData)
      ? formData.contractId?.value
      : undefined,
    vehicleType: formData.vehicleType?.label,
    vehicleNumber: formData.vehicleNumber,
    placementDateTime: formData.placementDateTime,
    rate: formData.rate,
    advance: formData.advance,
    balance: formData.balance,
    loadingUnloadingCharges: formData.loadingUnloadingCharges,
    margin: formData.margin,
    marginPercent: formData.marginPercent,
    calculationOnExtraLoading: formData.calculationOnExtraLoading,
    remarks: formData.remarks,
    tat: formData.tat,
    waypoints: formData.waypoints.map((waypoint) => ({
      name: waypoint.label,
      code: waypoint.value,
    })),
  })

  const handleSubmit = () => {
    const payload = createSubmissionPayload(formData)

    startTransition(async () => {
      // console.log('Submitting form data:', formData)
      const result = await createLoadBoard(payload)
      if (result && result.code === 200) {
        appDispatch(showSuccessAlert('Load Created Successfully'))
        navigate({
          pathname: home,
        })
      }
    })
  }

  return { handleSubmit, isPending }
}
