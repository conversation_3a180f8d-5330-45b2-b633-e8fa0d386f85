import { useCallback, useReducer } from 'react'
import {
  CreateLoadAutoCompleteOptionsType,
  CreateLoadFormDataType,
  LoadingAutoCompleteOptionsType,
} from '@/pages/createLoad/types/CreateLoadModel'
import {
  setErrors,
  setFormData,
} from '@/pages/createLoad/reducers/CreateLoadActions'
import { useAutoCompleteData } from '@/pages/createLoad/hooks/useAutoCompleteData'
import { useFormValidation } from '@/pages/createLoad/hooks/useFormValidation'
import { useFormSubmission } from '@/pages/createLoad/hooks/useFormSubmission'
import { useAutoCompleteEffects } from '@/pages/createLoad/hooks/useAutoCompleteEffects'
import CreateLoadReducer, {
  CREATE_LOAD_MODULE_STATE,
} from '@/pages/createLoad/reducers/CreateLoadReducer'

type UseCreateLoadReturnType = [
  formData: CreateLoadFormDataType,
  autoCompleteOptions: CreateLoadAutoCompleteOptionsType,
  isLoadingOptions: LoadingAutoCompleteOptionsType,
  error: Partial<Record<keyof CreateLoadFormDataType, string | undefined>>,
  handleChange: (field: keyof CreateLoadFormDataType, value: any) => void,
  isPending: boolean,
  handleSubmit: () => void,
]

const useCreateLoad = (): UseCreateLoadReturnType => {
  const [state = CREATE_LOAD_MODULE_STATE, dispatch] = useReducer(
    CreateLoadReducer,
    CREATE_LOAD_MODULE_STATE
  )

  const { formData, autoCompleteOptions, isLoadingOptions, errors } = state

  const autoCompleteFetchers = useAutoCompleteData(dispatch)
  const { validateForm } = useFormValidation(formData)
  const { handleSubmit: submitForm, isPending } = useFormSubmission(formData)

  useAutoCompleteEffects({
    formData,
    autoCompleteOptions,
    dispatch,
    fetchers: autoCompleteFetchers,
  })

  const handleChange = useCallback(
    (field: keyof CreateLoadFormDataType, value: any) => {
      dispatch(setFormData(field, value))

      if (errors?.[field]) {
        dispatch(setErrors({ ...errors, [field]: undefined }))
      }
    },
    [errors]
  )

  const handleSubmit = () => {
    const { isValid, errors: formErrors } = validateForm()

    if (isValid) {
      submitForm()
    } else {
      dispatch(setErrors(formErrors))
    }
  }

  return [
    formData,
    autoCompleteOptions,
    isLoadingOptions,
    errors,
    handleChange,
    isPending,
    handleSubmit,
  ]
}

export default useCreateLoad
