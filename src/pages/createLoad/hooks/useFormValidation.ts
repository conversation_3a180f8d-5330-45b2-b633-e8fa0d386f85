import { CreateLoadFormDataType } from '@/pages/createLoad/types/CreateLoadModel'
import { useAppDispatch } from '@/redux/store'
import { useCallback } from 'react'
import { createLoadSchema } from '../utils'
import { validateFormData } from '@/utils/ValidationUtils'
import { isObjectEmpty } from '@/utils/StringUtils'
import { showErrorAlert } from '@/redux/slices/AppSlice'

export const useFormValidation = (formData: CreateLoadFormDataType) => {
  const appDispatch = useAppDispatch()

  const validateForm = useCallback(() => {
    const {
      isValid,
      errors: formErrors,
      alertErrors,
    } = validateFormData(formData, createLoadSchema)

    if (!isObjectEmpty(alertErrors)) {
      appDispatch(showErrorAlert(alertErrors[Object.keys(alertErrors)[0]]))
    }

    return { isValid, errors: formErrors ?? {} }
  }, [appDispatch, formData])

  return { validateForm }
}
