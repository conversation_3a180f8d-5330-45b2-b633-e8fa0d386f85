import { Dispatch } from 'react'
import { setAutoCompleteOptions } from '@/pages/createLoad/reducers/CreateLoadActions'
import { CreateLoadAutoCompleteOptionsType } from '@/pages/createLoad/types/CreateLoadModel'
import { useAsyncThunk } from '@/hooks/useAsyncThunk'
import { OptionType } from '@/components/widgets/widgetsInterfaces'
import {
  getAllLoadTypes,
  getContractIds,
  getCustomers,
  getIndentTypes,
  getLocations,
  getVehicleTypes,
} from '@/api/serviceActions/CreateLoadServiceActions'
import {
  setAutoCompleteList,
  setAutoCompleteListWithData,
} from '@/utils/DataUtils'

export const useAutoCompleteData = (dispatch: Dispatch<any>) => {
  const updateAutoComplete =
    <T>(
      key: keyof CreateLoadAutoCompleteOptionsType,
      labelKey: string,
      valueKey: string,
      formatter: (data: T[], labelKey: string, valueKey: string) => OptionType[]
    ) =>
    (data: T[]) => {
      const formatted = formatter(data, labelKey, valueKey)
      dispatch(setAutoCompleteOptions(key, formatted))
      return formatted
    }

  const { execute: fetchAllLoadTypes } = useAsyncThunk(getAllLoadTypes, {
    immediate: false,
    formatter: updateAutoComplete(
      'loadTypes',
      'name',
      'id',
      setAutoCompleteList
    ),
  })
  const { execute: fetchVehicleTypes } = useAsyncThunk(getVehicleTypes, {
    immediate: false,
    formatter: updateAutoComplete(
      'vehicleTypes',
      'name',
      'id',
      setAutoCompleteListWithData
    ),
  })

  const { execute: fetchIndentTypes } = useAsyncThunk(getIndentTypes, {
    immediate: false,
  })

  const { execute: fetchCustomers } = useAsyncThunk(getCustomers, {
    immediate: false,
  })

  const { execute: fetchLocations } = useAsyncThunk(getLocations, {
    immediate: false,
  })

  const { execute: fetchContractIds } = useAsyncThunk(getContractIds, {
    immediate: false,
  })

  return {
    fetchAllLoadTypes,
    fetchVehicleTypes,
    fetchIndentTypes,
    fetchCustomers,
    fetchLocations,
    fetchContractIds,
  }
}
