import * as Path from '@/constant/RoutePath'
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted'

export const rowsPerPageOptions = [25, 50, 100]

export enum DispatchPeriodsEnum {
  Today = 'Today',
  Last_Week = 'Last Week',
  Last_Month = 'Last Month',
  Last_Year = 'Last Year',
  Custom = 'Custom',
}

export const loadsListingStatusArray = ['Pending', 'Rejected', 'Approved']

export enum loadsListingAPIStatusEnum {
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
}

export enum loadsFulfillmentByValueLabelMapper {
  MARKET = 'Market',
  OWNED = 'Owned',
}
export enum loadsOrderNumberValueLabelMapper {
  ORDER = 'Order',
}
export enum loadsOrderStatusValueLabelMapper {
  IN_TRANSIT = 'In Transit',
}
export enum loadsStatusValueLabelMapper {
  ASSIGNED = 'Assigned',
  UNASSIGNED = 'Unassigned',
}

export enum orchestrationRunningStatusEnum {
  RUNNING = 'Running',
  COMPLETED = 'Completed',
  FAILED = 'Failed',
}

export const headerMenuButtons = [
  {
    name: '/',
    label: 'Loads',
    icon: <FormatListBulletedIcon />,
    routePath: Path.home,
  },
]

export const documentModalTitle = [
  {
    lr: 'LR Receipt',
    eway: 'E-way Bill',
    podFrontBack: 'POD',
    invoice: 'Invoice',
  },
]
