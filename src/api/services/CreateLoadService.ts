import { AxiosInstance } from 'axios'
import {
  getLoadTypesUrl,
  getIndentTypesUrl,
  getCustomersUrl,
  getLocationsUrl,
  getVehicleTypesUrl,
  getContractIdsUrl,
  createLoadUrl,
} from '@/api/ServiceUrl'

export default (api: AxiosInstance) => {
  function getAllLoadTypes() {
    return api.get(getLoadTypesUrl)
  }

  function getIndentTypes() {
    return api.get(getIndentTypesUrl)
  }

  function getCustomers() {
    return api.get(getCustomersUrl)
  }

  function getLocations({ id }: { id: number }) {
    return api.get(getLocationsUrl, { params: { id } })
  }

  function getVehicleTypes() {
    return api.get(getVehicleTypesUrl)
  }

  function getContractIds({
    customer_id,
    start_point,
    destination_point,
  }: {
    customer_id: number
    start_point: string
    destination_point: string
  }) {
    return api.get(getContractIdsUrl, {
      params: { customer_id, start_point, destination_point },
    })
  }

  function createLoad(payload: any) {
    return api.post(createLoadUrl, payload)
  }

  return {
    getAllLoadTypes,
    getIndentTypes,
    getCustomers,
    getLocations,
    getVehicleTypes,
    getContractIds,
    createLoad,
  }
}
