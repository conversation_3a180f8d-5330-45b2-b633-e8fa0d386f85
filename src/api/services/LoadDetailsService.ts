import { AxiosInstance } from 'axios'
import {
  acceptLoadUrl,
  creatIndentUrl,
  getLoadDetailsUrl,
  getMoveToLoadBoardUrl,
  getVendorAnalyticsUrl,
  rejectLoadUrl,
} from '@/api/ServiceUrl'

export default (api: AxiosInstance) => {
  function getLoadDetails(id: any) {
    return api.get(getLoadDetailsUrl, {
      params: { id },
    })
  }
  function rejectLoad(id: any) {
    return api.put(rejectLoadUrl, id)
  }
  function acceptLoad(id: any) {
    return api.put(acceptLoadUrl, id)
  }
  function fetchVendorAnalytics() {
    return api.get(getVendorAnalyticsUrl)
  }
  function moveToLoadBoard(payload: any) {
    return api.get(getMoveToLoadBoardUrl, payload)
  }

  function createIndent(body: any) {
    return api.post(creatIndentUrl, body)
  }

  return {
    getLoadDetails,
    rejectLoad,
    acceptLoad,
    fetchVendorAnalytics,
    moveToLoadBoard,
    createIndent,
  }
}
