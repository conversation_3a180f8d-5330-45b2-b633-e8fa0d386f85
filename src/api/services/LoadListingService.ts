import { AxiosInstance } from 'axios'
import {
  getAllLoadsUrl,
  getCustomersUrl,
  getIndentTypesUrl,
  getLoadTypesUrl,
  getLocationsUrl,
  getVehicalNumbersUrl,
  getVehicleTypesUrl,
} from '@/api/ServiceUrl'

export default (api: AxiosInstance) => {
  async function getAllLoads(params: any) {
    const response = await api.get(getAllLoadsUrl, {
      params: {
        ...params,
      },
    })
    return response
  }
  async function getAllCustomers() {
    const response = await api.get(getCustomersUrl)
    return response
  }
  async function getVehicalNumbers() {
    const response = await api.get(getVehicalNumbersUrl)
    return response
  }
  async function getVehicalType() {
    const response = await api.get(getVehicleTypesUrl)
    return response
  }
  async function getLoadTypes() {
    const response = await api.get(getLoadTypesUrl)
    return response
  }
  async function getIndentTypes() {
    const response = await api.get(getIndentTypesUrl)
    return response
  }
  async function getOriginsByCustomerId(customerId: string) {
    const response = await api.get(getLocationsUrl, {
      params: {
        id: customerId,
      },
    })
    return response
  }

  return {
    getAllLoads,
    getAllCustomers,
    getOriginsByCustomerId,
    getVehicalNumbers,
    getVehicalType,
    getLoadTypes,
    getIndentTypes,
  }
}
