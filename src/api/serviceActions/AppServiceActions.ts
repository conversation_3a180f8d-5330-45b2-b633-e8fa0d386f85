import { createAsyncThunk } from '@reduxjs/toolkit'
import { app } from '@/api/services'
import { handleApiError } from '@/utils/ErrorHandleUtils'
import { AppDispatch } from '@/redux/store'
import { UserData } from '@/api/types'

export const getUserProfileData = createAsyncThunk<
  UserData,
  void,
  { rejectValue: string }
>('user/getUserProfileData', async (_, { dispatch, rejectWithValue }) => {
  try {
    const response = await app.getUserProfile()
    const responseData = response as unknown as {
      code: number
      details: any
    }
    if (responseData && responseData.code === 200) {
      return responseData.details
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})
