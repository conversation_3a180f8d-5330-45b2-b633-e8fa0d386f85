import { AppDispatch } from '@/redux/store'
import { handleApiError } from '@/utils/ErrorHandleUtils'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { createLoadService } from '@/api/services'
import {
  LoadType,
  IndentType,
  Customer,
  Location,
  VehicleType,
  Contract,
} from '@/api/types'

export const getAllLoadTypes = createAsyncThunk<
  LoadType[],
  void,
  { rejectValue: string }
>('getAllLoadTypes', async (_, { dispatch, rejectWithValue }) => {
  try {
    const response = await createLoadService.getAllLoadTypes()
    const responseData = response as unknown as {
      code: number
      details: any
    }

    if (responseData && responseData?.code === 200) {
      return responseData.details
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const getVehicleTypes = createAsyncThunk<
  VehicleType[],
  void,
  { rejectValue: string }
>('getVehicleTypes', async (_, { dispatch, rejectWithValue }) => {
  try {
    const response = await createLoadService.getVehicleTypes()
    const responseData = response as unknown as {
      code: number
      details: any
    }

    if (responseData && responseData?.code === 200) {
      return responseData.details
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const getIndentTypes = createAsyncThunk<
  IndentType[],
  void,
  { rejectValue: string }
>('getIndentTypes', async (_, { dispatch, rejectWithValue }) => {
  try {
    const response = await createLoadService.getIndentTypes()
    const responseData = response as unknown as {
      code: number
      details: any
    }

    if (responseData && responseData?.code === 200) {
      return responseData.details
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const getCustomers = createAsyncThunk<
  Customer[],
  void,
  { rejectValue: string }
>('getCustomers', async (_, { dispatch, rejectWithValue }) => {
  try {
    const response = await createLoadService.getCustomers()
    const responseData = response as unknown as {
      code: number
      details: any
    }

    if (responseData && responseData?.code === 200) {
      return responseData.details
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const getLocations = createAsyncThunk<
  Location[],
  { id: number },
  { rejectValue: string }
>('getLocations', async (args, { dispatch, rejectWithValue }) => {
  try {
    const response = await createLoadService.getLocations(args)
    const responseData = response as unknown as {
      code: number
      details: any
    }

    if (responseData && responseData?.code === 200) {
      return responseData.details?.[0]?.location || []
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const getContractIds = createAsyncThunk<
  Contract[],
  { customer_id: number; start_point: string; destination_point: string },
  { rejectValue: string }
>('getLocations', async (args, { dispatch, rejectWithValue }) => {
  try {
    const response = await createLoadService.getContractIds(args)
    const responseData = response as unknown as {
      code: number
      details: any
    }
    if (responseData && responseData?.code === 200) {
      return responseData.details
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const createLoad = createAsyncThunk<any, any, { rejectValue: string }>(
  'createLoad',
  async (args, { dispatch, rejectWithValue }) => {
    try {
      const response = await createLoadService.createLoad(args)
      return response
    } catch (error: any) {
      handleApiError(error.message, dispatch as AppDispatch)
      return rejectWithValue(error.message)
    }
  }
)
