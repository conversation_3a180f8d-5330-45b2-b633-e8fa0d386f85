import { handleApiError } from '@/utils/ErrorHandleUtils'
import { loadDetailsService } from '../services'
import { Dispatch } from '@reduxjs/toolkit'

export const getLoadDetails =
  (id: any): any =>
  async (dispatch: Dispatch) => {
    try {
      const responseAxios = await loadDetailsService.getLoadDetails(id)
      return responseAxios
    } catch (error: any) {
      handleApiError(error.message, dispatch)
    }
  }

export const rejectLoad =
  (id: any): any =>
  async (dispatch: Dispatch) => {
    try {
      const response = await loadDetailsService.rejectLoad(id)
      return response
    } catch (error: any) {
      handleApiError(error.message, dispatch)
    }
  }

export const acceptLoad =
  (id: any): any =>
  async (dispatch: Dispatch) => {
    try {
      const response = await loadDetailsService.acceptLoad(id)
      return response
    } catch (error: any) {
      handleApiError(error.message, dispatch)
    }
  }

export const fetchVendorAnalytics = () => async (dispatch: Dispatch) => {
  try {
    const response = await loadDetailsService.fetchVendorAnalytics()
    return response
  } catch (error: any) {
    handleApiError(error.message, dispatch)
  }
}

export const createIndent =
  (body: any): any =>
  async (dispatch: Dispatch) => {
    try {
      const response = await loadDetailsService.createIndent(body)
      return response
    } catch (error: any) {
      handleApiError(error.message, dispatch)
    }
  }

export const moveToLoadBoard =
  (payload: any): any =>
  async (dispatch: Dispatch) => {
    try {
      const response = await loadDetailsService.moveToLoadBoard(payload)
      return response
    } catch (error: any) {
      handleApiError(error.message, dispatch)
    }
  }
