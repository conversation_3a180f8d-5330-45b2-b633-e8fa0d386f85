import { createAsyncThunk, Dispatch } from '@reduxjs/toolkit'
import { loadListService } from '../services'
import { handleApiError } from '@/utils/ErrorHandleUtils'
import { LoadListDetails } from '@/api/types'
import { AppDispatch } from '@/redux/store'
import { LoadListFilterQueryParams } from '@/pages/loads/listing/LoadListingModels'

export const getLoadListing = createAsyncThunk<
  LoadListDetails,
  LoadListFilterQueryParams,
  { rejectValue: string }
>('getAllLoadTypes', async (args, { dispatch, rejectWithValue }) => {
  try {
    const response = await loadListService.getAllLoads(args)
    const responseData = response as unknown as {
      code: number
      details: LoadListDetails[]
    }

    if (responseData && responseData?.code === 200) {
      return responseData.details?.[0]
    } else {
      return rejectWithValue('No response received')
    }
  } catch (error: any) {
    handleApiError(error.message, dispatch as AppDispatch)
    return rejectWithValue(error.message)
  }
})

export const getCustomers = (): any => async (dispatch: Dispatch) => {
  return loadListService
    .getAllCustomers()
    .then((responseAxios: any) => responseAxios)
    .catch((error: any) => {
      handleApiError(error.message, dispatch)
    })
}

export const getIndentTypes = (): any => async (dispatch: Dispatch) => {
  return loadListService
    .getIndentTypes()
    .then((responseAxios: any) => responseAxios)
    .catch((error: any) => {
      handleApiError(error.message, dispatch)
    })
}

export const getLoadTypes = (): any => async (dispatch: Dispatch) => {
  return loadListService
    .getLoadTypes()
    .then((responseAxios: any) => responseAxios)
    .catch((error: any) => {
      handleApiError(error.message, dispatch)
    })
}

export const getOrigins =
  (id: string): any =>
  async (dispatch: Dispatch) => {
    return loadListService
      .getOriginsByCustomerId(id)
      .then((responseAxios: any) => responseAxios)
      .catch((error: any) => {
        handleApiError(error.message, dispatch)
      })
  }

export const getVehicalNumbers = (): any => async (dispatch: Dispatch) => {
  return loadListService
    .getVehicalNumbers()
    .then((responseAxios: any) => responseAxios)
    .catch((error: any) => {
      handleApiError(error.message, dispatch)
    })
}

export const getVehicalType = (): any => async (dispatch: Dispatch) => {
  return loadListService
    .getVehicalType()
    .then((responseAxios: any) => responseAxios)
    .catch((error: any) => {
      handleApiError(error.message, dispatch)
    })
}
