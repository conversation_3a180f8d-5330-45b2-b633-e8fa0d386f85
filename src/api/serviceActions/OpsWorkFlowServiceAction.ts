import { Dispatch } from 'redux'
import { opsWorkflow } from '../services'
import { handleApiError } from '@/utils/ErrorHandleUtils'

export const orchestrationToken =
  (queryParams: any): any =>
  async (dispatch: Dispatch) => {
    return opsWorkflow
      .orchestrationToken(queryParams)
      .then((responseAxios: any) => responseAxios.details)
      .catch((error: any) => {
        handleApiError(error.message, dispatch)
      })
  }
