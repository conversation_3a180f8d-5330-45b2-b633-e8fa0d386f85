import Axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { legacyToken, auth } from './ServiceUrl'

const defaultTimeOut: number = 300000
const DEBUG = import.meta.env.DEV
const BASE_URL: string = '/'

const Api = Axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: defaultTimeOut,
})

Api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    /** In dev, intercepts request and logs it into console for dev */
    if (DEBUG) {
      // add token for legacy system
      if (config && config.url && config.url.includes('_svc/legacy/')) {
        config.headers['Authorization'] = legacyToken
      } else {
        config.headers['Authorization'] = auth
      }
      // console.info('Service Request', config);
    }
    return config
  },
  (error) => {
    if (DEBUG) {
      console.error('Service Error', error)
    }
    return Promise.reject(error)
  }
)

/**
 * Passes response.data to services.
 * In dev, intercepts response and logs it into console for dev
 */

Api.interceptors.response.use(
  (response: AxiosResponse) => {
    if (DEBUG) {
      console.info('Service Response', response)
    }
    // return response.data;
    try {
      // Need to change after Backend changes
      return Promise.resolve(response.data)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  (error) => {
    if (
      !import.meta.env.DEV &&
      error.response &&
      error.response.status === 401
    ) {
      try {
        const hostName = window.location.hostname
        const firstDotIndex = hostName.indexOf('.')
        const env =
          firstDotIndex === -1
            ? hostName
            : hostName.substring(firstDotIndex + 1)
        const isLocalOrDev = ['gobolt.dev', 'gobolt.live'].includes(env)

        const redirectTo = isLocalOrDev
          ? `https://auth.${env}/`
          : `https://login.${env}/`

        window.location.replace(redirectTo)
      } catch (e) {
        // Raven.captureException(e);
      }
      throw new Axios.Cancel('Unauthorized')
    }
    if (error.response && error.response.data) {
      if (DEBUG) {
        console.error('Error: ', error.response)
      }
      return Promise.reject(error.response.data)
    }
    if (DEBUG) {
      console.error('Service Error', error)
    }
    return Promise.reject(error)
  }
)

export default Api
