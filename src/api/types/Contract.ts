import { VehicleType } from '@/api/types/VehicleType'
import { GeoPoint } from '@/api/types/Location'

export type Contract = {
  multipleStopage: MultipleStopage[]
  contractId: string
  tat?: string
  vehicleType?: VehicleType
}

export interface MultipleStopage {
  sLocation: StopageLocation
  stopageTime: number
}

export interface StopageLocation {
  id: string
  locationCity: LocationCity
  locationCode: string
  locationName: string
  locationPoint: GeoPoint
}

export interface LocationCity {
  id: number
  name: string
  cityCode: string
}
