import { Pagination } from '@/types/Pagination'

export interface LoadList {
  details: LoadListDetails[]
}

export interface LoadListDetails {
  pagination: Pagination
  data: LoadItemDetail[]
}

export interface LoadItemDetail {
  id: string
  loadType: string
  customerName: string
  originName: string
  destinationName: string
  tat: number
  vehicleType: string
  vehicleNumber: string
  placementDateTime: Date
  rate: number
  advance: number
  balance: number
  loadingUnloadingCharges: number
  margin: number
  marginPercentage: number
  calculationOnExtraLoading: string
  remarks: string
  createdAt: Date
  updatedAt: Date
  createdBy: string
  status: string
  driverMobileNumber: string
  buyerMobileNumber: string
  driverName: string
}
