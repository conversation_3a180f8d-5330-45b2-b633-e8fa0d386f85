export const legacyToken =
  'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik5ESkdSVVV3T0VFMk1rVTNRVGRGUkVNeE16SkVSVVpFUXpWQk1UVTRPRGc0TURJM1F6RXpRdyJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fJHYJIwuzzwUU2kjaL_3s-GylfsuT2smmmlcHBd-kdTNaHAYtV38T5xXj6FvaBc43EKMjpg8yyDD25uMQV2M-PJ-XUvyfPxvJRW-5h73laELFbnRbjvzGcdLyzZjiyi4nD51cVvRXI6caiIJ5jm2-CQxBXYbShsyxsDGdedO6yT__t-DGx1ZO_tKZzCy_H1psChU9aH5T38F89H8vmjjEfFYXem7P9IDq5kSp8UPpK5qn4B3qIGjTycFbL8CoEfd9DizlC-Kwy9mh3Jhfsa9sD_wfCnlDsLLRZYN0bFV_HujZj7sIemVokXzsVtcqhbvMESbfF9qug7fWtK79Mchow'
export const auth =
  'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik5ESkdSVVV3T0VFMk1rVTNRVGRGUkVNeE16SkVSVVpFUXpWQk1UVTRPRGc0TURJM1F6RXpRdyJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fJHYJIwuzzwUU2kjaL_3s-GylfsuT2smmmlcHBd-kdTNaHAYtV38T5xXj6FvaBc43EKMjpg8yyDD25uMQV2M-PJ-XUvyfPxvJRW-5h73laELFbnRbjvzGcdLyzZjiyi4nD51cVvRXI6caiIJ5jm2-CQxBXYbShsyxsDGdedO6yT__t-DGx1ZO_tKZzCy_H1psChU9aH5T38F89H8vmjjEfFYXem7P9IDq5kSp8UPpK5qn4B3qIGjTycFbL8CoEfd9DizlC-Kwy9mh3Jhfsa9sD_wfCnlDsLLRZYN0bFV_HujZj7sIemVokXzsVtcqhbvMESbfF9qug7fWtK79Mchow'

export const getUserProfileUrl = '/_svc/load-board-backend/v1/get-access-roles'
export const getLoadTypesUrl = '/_svc/load-board-backend/v1/load-types-list'
export const getIndentTypesUrl = '/_svc/load-board-backend/v1/indent-types-list'
export const getCustomersUrl = '/_svc/load-board-backend/v1/ops-customer-list'
export const getLocationsUrl =
  '/_svc/load-board-backend/v1/ops-origin-destination-list'
export const getContractIdsUrl = '/_svc/load-board-backend/v1/ops-contract-list'
export const getVehicleTypesUrl = '/_svc/load-board-backend/v1/ops-vehicle-list'
export const createLoadUrl = '/_svc/load-board-backend/v1/create-load-board'
export const getAllLoadsUrl = '/_svc/load-board-backend/v1/load-board-list'
export const getLoadDetailsUrl = '/_svc/load-board-backend/v1/get-load-board'
export const getVehicalNumbersUrl = '/'
export const rejectLoadUrl = '/_svc/load-board-backend/v1/reject-load-board'
export const acceptLoadUrl = '/_svc/load-board-backend/v1/approve-load-board'
export const getVendorAnalyticsUrl =
  '/_svc/load-board-backend/v1/ops-vendor-list'
export const getMoveToLoadBoardUrl =
  '/_svc/load-board-backend/v1/ops-move-to-load-board'
export const creatIndentUrl = '/_svc/load-board-backend/v1/ops-create-indent'
export const orchestrationUrl = '/_svc/load-board-backend/v1/ops-orchestration'
export const orchestrationTokenUrl =
  '/_svc/load-board-backend/v1/ops-orchestration'
