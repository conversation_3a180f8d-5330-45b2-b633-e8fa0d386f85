@use './_variables' as *;

.date-time-wrap{
    label{
        font-size: 12px;
        color: #143751;
        font-weight: 500;
        margin-bottom: 4px;
        .mandatory-flied{
            color: $red-color;
            margin-left: 4px;
        }
    }
    .MuiInputBase-input{
        color: #143751;
        font-size: 15px;
        padding: 0 14px;
        height: 44px;
        border-radius: 3px;
        border: solid 2px #E0E4E6;
        box-sizing: border-box;
        &::before,
        &::after {
            display: none;
        }
    }
    .standard{
        .MuiInputBase-input{
            border: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.42);
            border-radius: 0;
            padding: 0;
        }
    }
    .MuiOutlinedInput-notchedOutline{
        border: none;
    }
}
.MuiFormControl-root{width: 100%;}

// date time picker 
.MuiPickersLayout-root{
    .MuiDateTimePickerTabs-root{
        .MuiButtonBase-root.Mui-selected{
            color: $orange-color;
        }
         .MuiTabs-indicator{
            background-color: $orange-color;
        }
    }
    .MuiPickersLayout-contentWrapper{
         .MuiDateCalendar-root{
            .MuiPickersDay-root{
                &.Mui-selected{
                    background-color: $orange-color;
                    &:hover{
                        background-color: $orange-color;
                    }
                }
                &:hover{
                    background-color: rgba(247, 121, 30, 0.50);
                    color: $white-color;
                }
            }
        }
        .MuiTimeClock-root{
            .MuiClock-pin,.MuiClockPointer-root{
                background-color: $orange-color;
            }
            .MuiClockPointer-thumb{
                background-color: $orange-color;
                border: 16px solid $orange-color;
            }
        }
    }
    .MuiPickersLayout-actionBar{
        .MuiButton-root{
            color: $orange-color;
            &:hover{
                background: rgba(247, 121, 30, 0.06);
            }
        }
    }
}
// @media (pointer: fine) {
//      .MuiDateCalendar-root{
//         .MuiButtonBase-root.MuiPickersDay-root{
//             &:hover{
//                 background-color: rgba(247, 121, 30, 0.70) !important;
//                 color: $white-color !important;
//             }
//         }
//     }
// }



