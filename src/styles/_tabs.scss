.tabs-nav {
  box-shadow: 0px 1px 3px #0000000d;
  .main-tabs-nav {
    display: flex;
    padding-right: 10px;
    padding-left: 10px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.0509803922);
    background-color: #ffffff;
    height: 56px;
    .MuiTabs-root {
      .MuiTab-root {
        text-transform: capitalize;
        font-size: 16px;
        color: #768a9e;
        font-weight: 400;
        border-bottom: 2px solid transparent;
        min-width: 130px;
        padding-top: 16px;
        padding-bottom: 16px;

        &.MuiTab-root.Mui-selected {
          border-bottom: 2px solid #f7931e;
          color: #f7931e;
          margin: 0;
        }
      }
      .MuiTabs-indicator {
        background-color: #f7931e;
      }
    }
  }
  @media (max-width: 767px) {
    .MuiTabs-scrollButtons {
      display: inherit !important;
    }
    .main-tabs-nav {
      padding: 0;
      height: inherit;
      margin-bottom: 10px;
      border-radius: 4px;
      .MuiTabScrollButton-root.Mui-disabled {
        width: inherit;
        display: none !important;
      }
      .MuiTabs-root {
        .MuiTab-root {
          font-size: 13px;
          padding: 12px 32px;
          min-width: initial;
        }
      }
      .filterBtn {
        height: 32px;
        margin-left: 4px;
        font-size: 13px;
      }
    }
  }
}
