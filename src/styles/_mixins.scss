// mixins

// @mixin flexbox($direction: row, $justify:center, ) {
//     display: -webkit-box;
//     display: -moz-box;
//     display: -ms-flexbox;
//     display: -webkit-flex;
//     display: flex;
//     -webkit-justify-content: $justify;
//      -moz-justify-content: $justify;
//       -ms-justify-content: $justify;
//           justify-content: $justify;
//             -ms-flex-pack: $justify;
//     -webkit-flex-direction: $direction;
//       -ms-flex-direction: $direction;
//           flex-direction: $direction;
//   }

@mixin center( ) {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
  }