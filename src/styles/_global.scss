* {
  margin: 0;
  padding: 0;
  outline: none;
}
html,
body {
  height: 100%;
}
body {
  margin: 0px;
  padding: 0px;
  font-size: 14px;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif !important;
  font-weight: 400;
  background: #f7f7f7;
}
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
::placeholder {
  font-weight: 400 !important;
  color: #acb6c0 !important;
  opacity: 1 !important;
}

:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-weight: 400 !important;
  color: #acb6c0 !important;
  opacity: 1 !important;
}

::-ms-input-placeholder {
  /* Microsoft Edge */
  font-weight: 400 !important;
  color: #acb6c0 !important;
  opacity: 1 !important;
}
#root {
  height: 100%;
}
// .hc100 {
//   height: calc(100% - 60px);
// }

.wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  .main-page {
    height: calc(100vh - 60px);
    .container-fluid {
      margin: 0;
    }
    .main-content {
      padding: 0px;
      .pagePadding {
        padding: 16px 20px;
      }
    }
    .content-detail {
      padding: 0;
      margin: 0;
      width: 100%;
      min-height: 100%;
      max-width: 390px;
      border-radius: 0;
      border: none;
      overflow: auto;
      background-color: #ffffff;
      box-shadow: 4px 3px 7px #00000038;
      .detail-header {
        border-bottom: none;
        padding: 6px 16px;
        border-bottom: 1px solid #e9eff4;
        background: #ffffff;
        border-radius: 0;
        .MuiCardHeader-title {
          opacity: 1;
          font-weight: normal;
        }
      }
    }

    .main-content,
    .content-detail {
      height: calc(100vh - 60px);
      overflow: auto;
      position: sticky;
      top: 0;
    }
    .page-content {
      position: relative;
    }
  }

  .link-wrap {
    color: #006cc9;
    cursor: pointer;
    font-size: 13px;
  }
  .page-container-wrapper {
    padding: 16px 20px;
    .data-not-found {
      height: 70vh;
      display: flex;
    }
  }
  @media screen and (max-width: 767px) {
    .main-page {
      .content-detail {
        max-width: 100%;
        height: calc(100vh);
        overflow: auto;
        position: fixed;
        top: 0;
        z-index: 1101;
        .detail-header {
          background-color: #083654;
          border-color: #083654;
          span,
          svg,
          .MuiCardHeader-title {
            color: #fff;
          }
        }
      }
      .main-content {
        padding: 0px;
      }
      .page-container-wrapper {
        padding: 8px 16px 20px;
      }
      .link-wrap {
        font-size: 12px;
      }
    }
    .mob-mr {
      margin: 10px 10px 0 0;
    }
    .fullTank {
      justify-content: space-between;
      .ftValue {
        min-width: 90px;
      }
    }
  }
  .badgeView {
    color: #f7931d;
    background: #ea973e15;
    font-size: 12px;
    padding: 2px 8px;
    height: 22px;
    line-height: 19px;
  }
  .chips-wrapper {
    padding: 10px 20px 0;
  }
  .info_tooltip_wrap {
    position: relative;
    .tool-tip-icon {
      position: absolute;
      z-index: 1;
      width: 128px;
      height: 40px;
      margin-top: 4px;
      opacity: 0;
    }
  }
}

// Accordion css
.custom-accordion.MuiAccordion-root {
  border: solid 1px #e0e4e6;
  box-shadow: none;
  border-radius: 4px;
  &.Mui-expanded,
  .MuiAccordionSummary-content.Mui-expanded {
    margin: 0;
  }
  &::before {
    display: none;
  }
  .MuiAccordionSummary-root {
    background: #fafafa;
    &.Mui-expanded {
      min-height: 48px;
    }
  }
  .MuiAccordionDetails-root {
    padding: 10px 15px 20px;
  }
}

label.error,
.MuiFormHelperText-root {
  margin: 3px 0 0 0 !important;
  font-size: 11px !important;
  line-height: 12px !important;
  color: #ed1b00 !important;
  font-weight: normal !important;
  position: absolute !important;
  right: 2px;
  top: 100%;
  z-index: 9;
}

.wrapper .imgOnClick {
  position: relative;
  background-color: transparent;
  margin: 0;
  padding: 0;
  margin-top: 4px;
  min-width: auto;
  .upImg {
    max-width: 40px;
    height: auto;
  }
  .count {
    position: absolute;
    top: -5px;
    right: -3px;
    background: #fff;
    color: #143751;
    border-radius: 22px;
    width: 17px;
    line-height: 17px;
    font-size: 11px;
    z-index: 2;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1607843137254902);
  }
}

.fullTank {
  .ftValue {
    min-width: 120px;
  }
}
