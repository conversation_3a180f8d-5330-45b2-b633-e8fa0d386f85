import { getUserProfileData } from '@/api/serviceActions/AppServiceActions'
import { UserData } from '@/api/types/User'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { RootState } from '@/redux/store'

export type AlertType = 'success' | 'error'

export type AlertModalState = {
  isOpen: boolean
  alertType: AlertType
  alertMessage: string
}

export type ViewDocumentModalState = {
  isOpen: boolean
  fileLinks: any[]
  title: string
}

export type AppState = {
  userInfo: UserData
  alertModal: AlertModalState
  isDrawerOpen: boolean
  headerMenu: string
  showLoader: boolean
  viewDocumentModal: ViewDocumentModalState
  startPoll: boolean
  stopPoll: boolean
}

const initialState: AppState = {
  userInfo: {
    email: '',
    roles: [],
  },
  alertModal: {
    isOpen: false,
    alertType: 'success',
    alertMessage: '',
  },
  isDrawerOpen: false,
  headerMenu: '',
  showLoader: false,
  viewDocumentModal: {
    isOpen: false,
    fileLinks: [],
    title: '',
  },
  startPoll: false,
  stopPoll: false,
}

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    openViewDocumentModal: (
      state,
      action: PayloadAction<{ links: any[]; title: string }>
    ) => {
      state.viewDocumentModal.isOpen = true
      state.viewDocumentModal.fileLinks = action.payload.links
      state.viewDocumentModal.title = action.payload.title
    },
    closeViewDocumentModal: (state) => {
      state.viewDocumentModal = {
        isOpen: false,
        fileLinks: [],
        title: '',
      }
    },
    showSuccessAlert: (state, action: PayloadAction<string>) => {
      state.alertModal.isOpen = true
      state.alertModal.alertType = 'success'
      state.alertModal.alertMessage = action.payload
    },
    showErrorAlert: (state, action: PayloadAction<string>) => {
      state.alertModal.isOpen = true
      state.alertModal.alertType = 'error'
      state.alertModal.alertMessage = action.payload
    },
    hideAlert: (state) => {
      state.alertModal.isOpen = false
      state.alertModal.alertMessage = ''
    },
    openDrawer: (state) => {
      state.isDrawerOpen = true
    },
    closeDrawer: (state) => {
      state.isDrawerOpen = false
    },
    setHeaderMenu: (state, action: PayloadAction<string>) => {
      state.headerMenu = action.payload
    },
    pollStart: (state, action: PayloadAction<any>) => {
      state.startPoll = action.payload
    },

    pollStop: (state, action: PayloadAction<any>) => {
      state.stopPoll = action.payload
    },
    showLoader: (state) => {
      state.showLoader = true
    },
    hideLoader: (state) => {
      state.showLoader = false
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getUserProfileData.fulfilled, (state, action) => {
      state.userInfo = action.payload
    })
  },
})

export const {
  showSuccessAlert,
  showErrorAlert,
  hideAlert,
  openDrawer,
  closeDrawer,
  setHeaderMenu,
  showLoader,
  hideLoader,
  openViewDocumentModal,
  closeViewDocumentModal,
  pollStart,
  pollStop,
} = appSlice.actions

export default appSlice.reducer

export const selectCurrentUser = (state: RootState) => state.app.userInfo
