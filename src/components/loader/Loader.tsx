import './Loader.scss'

interface LoaderProps {
  loading?: boolean
}
function Loader(props: LoaderProps) {
  const { loading = false } = props
  // const loading = useSelector(
  //   (state: any) => state.appReducer.showLoader,
  //   shallowEqual
  // )

  return (
    ((loading || props.loading) && (
      <div>
        <div className="page-loader">
          <img src="/images/loader.gif" alt="loader" />
        </div>
      </div>
    )) ||
    null
  )
}

export default Loader
