import Chip from '@mui/material/Chip'
import React from 'react'
import { isNullValue } from '@/utils/StringUtils'
import './Chips.scss'

interface ChipsProps {
  label: string
  onDelete?: React.EventHandler<any>
  variant?: 'filled' | 'outlined'
  style?: any
  className?: string
}

export default function Chips(props: ChipsProps) {
  const { variant, style, className } = props
  return !isNullValue(props.label) ? (
    <Chip
      className={className ? className : 'chip-btn'}
      label={props.label}
      onDelete={props.onDelete}
      variant={variant}
      style={style}
    />
  ) : null
}
