import { useSearchParams } from '@/hooks/useSearchParams'
import Chips from './Chips'
import { getAdvanceFilterChips } from '@/utils/Routerutils'
import { isObjectEmpty } from '@/utils/StringUtils'

interface FilterChipsProps {
  requestFiltersKeyValueObj: any
  onDelete: (
    element: any,
    removeFiltersQueryParams: (items: Array<any>) => void,
    keepRouteState?: boolean
  ) => void
  shouldKeepRouteState?: boolean
}

const FilterChips = (props: FilterChipsProps) => {
  const {
    requestFiltersKeyValueObj,
    onDelete,
    shouldKeepRouteState = false,
  } = props
  const [filterState, , removeFiltersQueryParams] = useSearchParams(
    requestFiltersKeyValueObj
  )

  return (
    <>
      {!isObjectEmpty(getAdvanceFilterChips(filterState.chips)) &&
        Object.keys(getAdvanceFilterChips(filterState.chips)).map(
          (element: any, index: any) => (
            <Chips
              key={index}
              label={filterState.chips[element]}
              onDelete={() =>
                onDelete(
                  element,
                  removeFiltersQueryParams,
                  shouldKeepRouteState
                )
              }
            />
          )
        )}
    </>
  )
}

export default FilterChips
