import React, { ReactNode, useEffect } from 'react'
import './FilterContainer.scss'
import {
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material'
import { ClearAll, Close, FilterList } from '@mui/icons-material'
import Button from '../../widgets/button/Button'

interface FilterContainerProps {
  children: ReactNode
  open: boolean
  onClose: any
  onClear?: any
  onApply: any
  title: string
  primaryButtonTitle: string
  secondaryButtonTitle?: string
  styleName?: string
  loading?: any
  titleElement?: any
  primaryLeftIcon?: any
  secondaryLeftIcon?: any
}

function FilterContainer(props: FilterContainerProps) {
  const {
    children,
    open,
    onClose,
    onApply,
    onClear,
    title,
    primaryButtonTitle,
    secondaryButtonTitle,
    loading,
    titleElement,
  } = props

  const descriptionElementRef = React.useRef<HTMLElement>(null)
  useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef
      if (descriptionElement !== null) {
        descriptionElement.focus()
      }
    }
  }, [open])

  return (
    <Dialog
      open={open}
      onClose={onClose}
      scroll={'paper'}
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
      className={`filter-modal ${props.styleName ? props.styleName : ''}`}
    >
      <DialogTitle id="scroll-dialog-title">
        {title}
        {titleElement ? (
          <>{titleElement}</>
        ) : (
          <IconButton aria-label="close" onClick={onClose}>
            <Close />
          </IconButton>
        )}
      </DialogTitle>
      <DialogContent>{children}</DialogContent>
      <DialogActions className="mobile-view-btn">
        {secondaryButtonTitle && (
          <Button
            title={secondaryButtonTitle}
            className="btn-orange close-btn"
            onClick={onClear}
            leftIcon={
              props.primaryLeftIcon ? props.primaryLeftIcon : <ClearAll />
            }
            loading={loading}
          />
        )}
        <Button
          title={primaryButtonTitle}
          onClick={onApply}
          className="btn-blue"
          leftIcon={
            props.secondaryLeftIcon ? props.secondaryLeftIcon : <FilterList />
          }
          loading={loading}
        />
      </DialogActions>
    </Dialog>
  )
}

export default FilterContainer
