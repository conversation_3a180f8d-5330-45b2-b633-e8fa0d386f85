.filter-modal {
  .MuiDialog-paper {
    width: 380px;
    border-radius: 0;
    position: absolute;
    right: 0;
    margin: 0;
    top: 0;
    max-height: initial;
    height: 100vh;
  }

  .MuiDialogTitle-root {
    padding: 8px 24px;
    height: 50px;
    background-image: linear-gradient(to bottom, #f9f9f9, #eaeff3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    font-weight: normal;
    line-height: 1.2;
    color: #143751;
  }

  .MuiDialogActions-root {
    padding: 10px 20px 20px;
    justify-content: center;
  }

  .MuiTypography-root + .MuiDialogContent-root {
    border: none;
    padding: 15px 20px;
  }

  .autosuggest-wrap .MuiInputBase-root {
    border-radius: 4px;
  }

  .MuiDialog-container {
    @media (max-width: 767px) {
      overflow-x: hidden;
      overflow-y: auto;
      text-align: center;
      display: block;
      background: #fbfbfb;
    }
  }
}

/* filter form row */
.filter-form-row {
  .form-group {
    margin-bottom: 15px;
  }
}

@media (max-width: 767px) {
  .filter-modal {
    .MuiDialog-paper {
      margin: 0;
      width: 100%;
      border-radius: 0;
      display: inline-block;
      text-align: left;
      vertical-align: middle;
      max-height: inherit;
      overflow-y: initial;
      position: relative;
      height: auto;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    }

    .MuiDialogTitle-root {
      padding: 0 15px;
      height: 48px;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.07);
      background: #083654;
      border-radius: 0;
      font-size: 18px;
      font-weight: 500;
      line-height: 1.14;
      color: #ffffff;

      .MuiSvgIcon-root {
        color: #ffffff;
      }
    }

    .MuiDialogContent-root {
      padding: 30px 15px 0;
      overflow: visible;
    }

    .MuiDialogActions-root {
      padding: 8px 15px 20px;
    }
  }

 

  .mobile-view-btn {
    .btn {
      width: 100%;
    }
    
    .close-btn {
      max-width: 116px;
    }
  }
}