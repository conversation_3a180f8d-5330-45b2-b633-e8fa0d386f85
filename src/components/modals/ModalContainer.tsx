import {
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material'
import { Close, GetApp } from '@mui/icons-material'
import React, { ReactNode } from 'react'
import Button from '@/components/widgets/button/Button'
import '@/components/modals/ModalContainer.scss'
import { isNullValue } from '@/utils/StringUtils'
import { SubmitTooltip } from '../widgets/SubmitToolTip'

interface ModalContainerProps {
  children: ReactNode
  open: boolean
  onClose: any
  onApply?: any
  onClear?: any
  title: any
  hideCloseButton?: boolean
  primaryButtonTitle?: string
  primaryButtonType?: 'button' | 'submit' | 'reset' | undefined
  primaryButtonStyle?: string
  secondaryButtonStyle?: string
  primaryButtonRightIcon?: any
  primaryButtonLeftIcon?: any
  primaryButtonDisable?: boolean
  secondaryButtonRightIcon?: any
  secondaryButtonLeftIcon?: any
  secondaryButtonTitle?: string
  secondaryButtonLoading?: boolean
  tertiaryButtonTitle?: string
  tertiaryButtonLeftIcon?: any
  tertiaryButtonDisable?: boolean
  tertiaryButtonLoading?: boolean
  tertiaryButtonRightIcon?: any
  tertiaryButtonStyle?: string
  onClickTertiary?: any
  styleName?: any
  loading?: boolean
  csvSample?: boolean
  secondaryButtonDisable?: boolean
  onClickDownloadSample?: any
  actionButtonStyle?: any
  orderCsvSampleMessage?: string
  showSearch?: boolean
  searchPlaceHolder?: string
  searchText?: any
  searchOnChange?: any
  showSearchIcon?: boolean
  btnClick?: any
  btnDisable?: boolean
  btnTitle?: string
  DialogActionsInfo?: any
  titleInfo?: any
  titleStyle?: ReactNode
}

function ModalContainer(props: ModalContainerProps) {
  const {
    title,
    children,
    open = false,
    onClose,
    onApply,
    onClear,
    csvSample,
    onClickDownloadSample,
    orderCsvSampleMessage,
    primaryButtonTitle,
    secondaryButtonTitle,
    primaryButtonLeftIcon,
    primaryButtonType,
    actionButtonStyle,
    primaryButtonStyle,
    secondaryButtonStyle,
    secondaryButtonLoading,
    onClickTertiary,
    styleName,
    tertiaryButtonTitle,
    secondaryButtonRightIcon,
    tertiaryButtonLeftIcon,
    tertiaryButtonDisable,
    tertiaryButtonLoading,
    tertiaryButtonRightIcon,
    tertiaryButtonStyle,
    secondaryButtonLeftIcon,
    primaryButtonRightIcon,
    loading,
    secondaryButtonDisable,
    primaryButtonDisable,
    hideCloseButton = false,
    titleInfo,
    titleStyle,
  } = props

  const descriptionElementRef = React.useRef<HTMLElement>(null)
  React.useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef
      if (descriptionElement !== null) {
        descriptionElement.focus()
      }
    }
  }, [open])

  return (
    <div>
      <Dialog
        open={open}
        onClose={() => {
          return false
        }}
        scroll={'paper'}
        aria-labelledby="scroll-dialog-title"
        aria-describedby="scroll-dialog-description"
        className={'custom-modal ' + styleName}
      >
        <DialogTitle id="scroll-dialog-title">
          <div
            className={`d-flex flex-fill align-items-center ${titleStyle ?? ''}`}
          >
            <span className="text-truncate">{title}</span>
            {titleInfo}
          </div>

          {!hideCloseButton && (
            <IconButton aria-label="close" onClick={onClose}>
              <Close />
            </IconButton>
          )}
        </DialogTitle>
        <DialogContent>{children}</DialogContent>
        {(!isNullValue(tertiaryButtonTitle) ||
          !isNullValue(secondaryButtonTitle) ||
          !isNullValue(primaryButtonTitle)) && (
          <DialogActions className={actionButtonStyle ? actionButtonStyle : ''}>
            {props.DialogActionsInfo && props.DialogActionsInfo}
            {!isNullValue(tertiaryButtonTitle) && (
              <Button
                title={tertiaryButtonTitle}
                className={
                  tertiaryButtonStyle ? tertiaryButtonStyle : 'btn-orange'
                }
                leftIcon={tertiaryButtonLeftIcon}
                rightIcon={tertiaryButtonRightIcon}
                disable={tertiaryButtonDisable}
                onClick={onClickTertiary}
                loading={tertiaryButtonLoading}
              />
            )}

            {!isNullValue(secondaryButtonTitle) && (
              <Button
                title={secondaryButtonTitle}
                className={
                  secondaryButtonStyle ? secondaryButtonStyle : 'btn-orange'
                }
                rightIcon={secondaryButtonRightIcon}
                leftIcon={secondaryButtonLeftIcon}
                disable={secondaryButtonDisable}
                onClick={onClear}
                loading={secondaryButtonLoading}
              />
            )}
            <>
              {csvSample && (
                <span className="download-csv" onClick={onClickDownloadSample}>
                  {orderCsvSampleMessage !== ' ' && (
                    <>
                      {' '}
                      <GetApp />
                      {orderCsvSampleMessage || 'Download CSV sample'}
                    </>
                  )}{' '}
                </span>
              )}
              {!isNullValue(primaryButtonTitle) &&
                (primaryButtonDisable ? (
                  <SubmitTooltip
                    title="Please select valid customer and contract ID to proceed"
                    arrow
                    placement="top"
                  >
                    <span>
                      <Button
                        title={primaryButtonTitle}
                        onClick={onApply}
                        loading={loading}
                        rightIcon={primaryButtonRightIcon}
                        leftIcon={primaryButtonLeftIcon}
                        type={primaryButtonType}
                        className={primaryButtonStyle || 'btn-blue'}
                        primaryButton={true}
                        disable={primaryButtonDisable}
                      />
                    </span>
                  </SubmitTooltip>
                ) : (
                  <Button
                    title={primaryButtonTitle}
                    onClick={onApply}
                    loading={loading}
                    rightIcon={primaryButtonRightIcon}
                    leftIcon={primaryButtonLeftIcon}
                    type={primaryButtonType}
                    className={
                      primaryButtonStyle ? primaryButtonStyle : 'btn-blue'
                    }
                    primaryButton={true}
                    disable={primaryButtonDisable}
                  />
                ))}
            </>
          </DialogActions>
        )}
      </Dialog>
    </div>
  )
}

export default ModalContainer
