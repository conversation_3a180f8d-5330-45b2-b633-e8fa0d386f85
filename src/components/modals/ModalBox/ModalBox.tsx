import ModalContainer from '@/components/modals/ModalContainer'
import '@/components/modals/ModalBox/ModalBox.scss'

interface ModalBoxProps {
  open: boolean
  onClose: any
  title?: string
  heading?: string
  styleName?: string
  icon?: any
  message?: string
  children?: any
  onClickTertiary?: () => void
  onClickSecondary?: () => void //for secondary button click
  loading?: boolean
  secondaryButtonLoading?: boolean
  tertiaryButtonLoading?: boolean
  secondaryButtonTitle?: string
  tertiaryButtonTitle?: string
  tertiaryButtonStyle?: string
  secondaryButtonStyle?: string
}

function ModalBox(props: ModalBoxProps) {
  const {
    open,
    onClose,
    children,
    heading,
    onClickTertiary,
    onClickSecondary,
    secondaryButtonLoading,
    tertiaryButtonLoading,
    secondaryButtonTitle,
    tertiaryButtonTitle,
    tertiaryButtonStyle,
    secondaryButtonStyle,
  } = props
  return (
    <ModalContainer
      open={open}
      secondaryButtonLoading={secondaryButtonLoading}
      tertiaryButtonLoading={tertiaryButtonLoading}
      secondaryButtonTitle={secondaryButtonTitle && secondaryButtonTitle}
      tertiaryButtonTitle={tertiaryButtonTitle && tertiaryButtonTitle}
      tertiaryButtonStyle={tertiaryButtonStyle && tertiaryButtonStyle}
      secondaryButtonStyle={secondaryButtonStyle && secondaryButtonStyle}
      onClose={() => {
        onClose()
      }}
      onClear={() => onClickSecondary && onClickSecondary()}
      onClickTertiary={() => onClickTertiary && onClickTertiary()}
      styleName={
        'containerModal' + ' ' + (props.styleName ? props.styleName : '')
      }
      actionButtonStyle="center"
      title={props.title}
    >
      <>
        <div className="text-center">
          {props.icon}
          <h2 className={'contentHeading'}>{heading}</h2>
          <label>{props.message}</label>
        </div>
        {children}
      </>
    </ModalContainer>
  )
}

export default ModalBox
