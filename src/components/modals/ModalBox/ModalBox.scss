/* message modal */
.containerModal.custom-modal {
	.MuiDialog-paper {
		min-width: 480px;
		max-width: 480px;
	}
	.MuiDialogContent-root {
		padding: 24px 10px 0;
		.MuiSvgIcon-root {
			font-size: 56px;
			line-height: 1.09;
			color: #ED1B00;
			opacity: 0.9;
		}
		.contentHeading {
			line-height: 1.18;
			text-align: center;
			margin: 10px 0 0;
			font-size: 24px;
			font-weight: 500;
		}
		.success {
			color: #1FC900;
		}
		.error {
			color: #ED1B00;
		}
		.info {
			color: #f7931e;
		}
		label {
			line-height: 20px;
			margin-bottom: 0;
			font-size: 17px;
			font-weight: 300;
			color: #768a9e;
			margin-top: 10px;
			max-width: 400px;
			word-break: break-word;
			white-space: break-spaces;
		}
	}
	.btn{
		padding: 10px 24px;
		min-width: 90px;
		line-height: normal;
	}
	.MuiDialogActions-root {
		justify-content: center !important;
		padding: 32px 0px 20px;
	}
	.center {
		.MuiDialogActions-root {
			.MuiDialogActions-spacing {
				justify-content: center;
			}
		}
	}
	.confirmData{
		justify-content: center;
		border-top: solid 1px #eee;
		border-bottom: solid 1px #eee;
		margin: 20px 0 8px;
		.item{
			margin: 12px 0;		
			text-align: center;	
			.label{
				font-size: 13px;
				color: #133751;
				opacity: 0.57;
				font-weight: 300;
				margin: 0;
				padding: 0;
			}
			.value{
				font-size: 15px;
				color: #083654;
				font-weight: 500;
				margin: 0;
			}
		}
    }
}

.containerModal.custom-modal.success .contentHeading{color: #1FC900;}
.containerModal.custom-modal.error .contentHeading{color: #ED1B00;}
.containerModal.custom-modal.info .contentHeading{color: #f7931e;}

.containerModal.custom-modal.success {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #1FC900;
		}
	}
}
.containerModal.custom-modal.error {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #ED1B00;
		}
	}
}
.containerModal.custom-modal.info {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #f7931e;
		}
	}
}

@media (min-width: 768px) {
	.smallModal.custom-modal {
		.MuiDialog-paper {
			min-width: 500px;
			max-width: 500px;
			width: 100%;
		}
	}
}
@media (max-width: 767px) {
	.containerModal.custom-modal {
		.MuiDialog-paper {
			min-width: 100%;
		}
	}
}


.upload-box-modal{
    border-radius: 10px;
    border: solid 1px #F77E1E;
    background-color: #fff;
    text-align: center;
    padding: 16px 0 20px;
    color: #768a9e;
    margin: 30px 20px 0px;
}
.upload-box-modal p{
    font-size: 13px;
    margin-bottom: 10px;
    color:#133751;
}
.upload-box-modal p + span {
    font-size: 10px;
    display: block;
    margin-bottom: 8px;
    line-height: 1.1;
}
.upload-box-modal .file-upload-btn,
.upload-box-modal .file-upload-btn:hover {
    width: auto;
}
.file-upload-wrap span{
    text-transform: uppercase;
}
.okBtn-grey{
	background: linear-gradient(270deg, #f9f9f9, #eaeff3);
    width: 81px;
    border: 1px solid #7070701D;
	font-size: 16px;
}
.upload-success.message-modal.custom-modal .MuiDialogActions-root {
    padding: 15px 0 25px;
}

.uploadedFile{
	display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 8px;	
	.imgWrap{
		position: relative;
		margin-right: 20px;
		.closeIcon{
			position: absolute;
			color: #fff !important;
			line-height: normal;
			border-radius: 50%;
			background: #BDBDBD 0% 0% no-repeat padding-box;
			border: 2px solid #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			right: -12px;
			top: -8px;
			.MuiSvgIcon-root{
				width: 18px;
				height: 18px;
				color: #fff !important;
			}
		}
		.upImg{
			max-width: 36px;
			height: auto;
		}
	}
	.checkIcon{
		color: #52A83E;
		margin-left: auto;      
	}
}
