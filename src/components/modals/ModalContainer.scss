// Variables
$primary-color: #083654;
$border-radius: 8px;

// Custom Modal
.custom-modal {
  .MuiDialog-container{
    overflow-x: hidden;
    overflow-y: auto;
    text-align: center;
    display: block;
  }
  
  .MuiDialog-paper {
    border-radius: $border-radius;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
    max-height: inherit;
    overflow-y: initial;
    min-width: 800px;
  }
  
  .MuiDialogContent-root {
    border: none;
    padding: 20px 25px 0;
    overflow: visible;
  }
  
  .MuiDialogTitle-root {
    padding: 8px 24px;
    height: 60px;
    background-image: linear-gradient(to bottom, #f9f9f9, #eaeff3);
    border-top-left-radius: $border-radius;
    border-top-right-radius: $border-radius;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    font-weight: 400;
    line-height: 1.2;
    color: $primary-color;
    width: 100%;
  }
  
  .MuiDialogActions-root {
    padding: 10px 25px 30px;
  }
  
}

// Medium Modal
// .medium-modal {
//   &.custom-modal .MuiDialog-paper {
//     @media (min-width: 768px) {
//       max-width: 665px;
//       min-width: 665px;
//     }
//   }
// }

// Media Queries
@media (max-width: 992px) {
  .custom-modal .MuiDialog-paper {
    min-width: 92%;
  }
}

@media (max-width: 767px) {
  .custom-modal {
    .MuiDialog-paper{
      margin: 0;
      min-width: 100% !important;
      border-radius: 0;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
      width: 100%;
    }
    
    .MuiDialogContent-root {
      border: none;
      padding: 20px 15px 0;
      overflow: visible;
    }
    
    .MuiDialogTitle-root {
      padding: 0 15px;
      height: 48px;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.07);
      background: $primary-color;
      border-radius: 0;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.14;
      color: #ffffff;
      
      .MuiSvgIcon-root {
        color: #ffffff;
      }
    }
    
    .MuiDialogActions-root {
      padding: 8px 15px 20px;
      text-align: right;
    }
    
    .MuiDialog-scrollPaper {
      box-shadow: none;
      background: #fbfbff;
    }
    
    .MuiDialogActions-spacing > :not(:first-child) {
      width: auto;
    }
  }
}