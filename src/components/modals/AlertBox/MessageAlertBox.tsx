import { shallowEqual, useSelector } from 'react-redux'
import ModalContainer from '@/components/modals/ModalContainer'
import './MessageAlertBox.scss'
import { CheckCircle, Report } from '@mui/icons-material'
import { RootState, useAppDispatch } from '@/redux/store'
import { hideAlert } from '@/redux/slices/AppSlice'

function MessageAlertBox() {
  const appDispatch = useAppDispatch()
  const alertModal = useSelector(
    (state: RootState) => state.app.alertModal,
    shallowEqual
  )

  return (
    (alertModal.isOpen &&
      alertModal.alertMessage &&
      alertModal.alertMessage !== '' && (
        <ModalContainer
          title={alertModal.alertType === 'success' ? 'Success' : 'Error'}
          secondaryButtonTitle={'OK'}
          secondaryButtonStyle="btn-orange btn-width-50"
          onClear={() => {
            appDispatch(hideAlert())
          }}
          open={alertModal.isOpen}
          onClose={() => {
            appDispatch(hideAlert())
          }}
          styleName={
            'message-modal' +
            (alertModal.alertType === 'success' ? ' success' : ' error')
          }
        >
          <div className="text-center">
            {alertModal.alertType === 'success' ? <CheckCircle /> : <Report />}
            <h2
              className={
                'content-heading' +
                (alertModal.alertType === 'success' ? ' success' : ' error')
              }
            >
              {alertModal.alertType === 'success' ? 'Success' : 'Error'}
            </h2>
            <label>{alertModal.alertMessage}</label>
          </div>
        </ModalContainer>
      )) ||
    null
  )
}

export default MessageAlertBox
