.pod-upload-modal{
    &.custom-modal {
    .MuiDialogContent-root {
      padding: 10px;
      
      @media (max-width: 767px) {
        img {
          width: 100%;
        }
      }
    }
  }
    .carousel .thumbs-wrapper {
        margin: 0;
    }
    .carousel.carousel-slider .control-arrow{
        top: 40%;
        height: 55px;
        width: 32px;
        border-radius: 2px;
        border: 1px solid #70707064;
        right:16px;
        background-color: #fff;
        opacity: 1;
        &:hover{
            background: #fff;
        }
    }
    .carousel .control-next.control-arrow:before {
        border-left: 8px solid #000;
    }
    .carousel .slide iframe{
        width: calc(100% - 20px);
        margin: 0;
    }
    .MuiDialogActions-root{
        padding: 0;
    }
    .MuiDialogContent-root{
        padding: 10px 0 0 0;
    }
    .carousel .control-prev.control-arrow:before {
        border-right: 8px solid #000;
    }
    .carousel .control-prev.control-arrow{
        left: 16px;
    }
}
.MuiButtonBase-root.upload_image_icon {
    position: relative;
    width: 36px;
    height: 36px;
    padding: 0;
    .value_show {
        position: absolute;
        top: -2px;
        right: -3px;
        background: #fff;
        border-radius: 22px;
        width: 17px;
        line-height: 17px;
        font-size: 12px;
        font-weight: 500;
        box-shadow: 0 3px 6px rgba(0,0,0,.1607843137254902);
    }
}