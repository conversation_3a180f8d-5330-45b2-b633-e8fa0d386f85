.filter-panel {
  position: relative;
  margin-bottom: 16px;
  .MuiTypography-root:after {
    background-color: #2c2c2c;
    bottom: 0;
    content: '';
    height: 2px;
    left: 0;
    position: absolute;
    width: 36px;
  }
  @media screen and (max-width: 767px) {
    // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.08);
    padding: 0px;
  }

  .btn-detail {
    color: #768a9e;
    &:hover {
      color: #ffffff;
    }
  }

  .MuiToolbar-root {
    min-height: 40px;
    max-height: 65px;
    padding: 0;
  }

  @media screen and (max-width: 767px) {
    .icon-space {
      svg {
        margin-right: 0;
      }
    }
  }
}

.icon-list .MuiSvgIcon-root {
  margin-right: 0 !important;
}

.filter-panel .icon-list {
  width: 50px;
}

.filter-panel .MuiTypography-root {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.17;
  color: #083654;
}

.legacy-heading {
  align-items: center;
  .legacy-currency {
    width: 48px;
    height: 48px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1fc900;
    border-radius: 50px;
    background-color: rgba(31, 201, 0, 0.2);
    margin-right: 18px;
    @media screen and (max-width: 767px) {
      width: 30px;
      height: 30px;
      margin-right: 8px;
    }
  }

  span {
    color: #1fc900;
    opacity: 1;
    display: block;
    font-size: 20px;
    @media screen and (max-width: 767px) {
      font-size: 14px;
    }
  }

  .legacy-name {
    color: #768a9e;
    font-size: 14px;
    @media screen and (max-width: 767px) {
      font-size: 10px;
    }
  }

  .legacy-price {
    color: #323232;
    font-size: 24px;
    font-weight: 700;
    @media screen and (max-width: 767px) {
      font-size: 14px;
    }
  }
}

@media (max-width: 767px) {
  .filter-panel {
    .mob-btn-blue,
    .btn-detail-mob {
      height: 34px;
      padding: 5px 12px;
      min-width: auto;
      background: #f9f9f9;
      background-image: linear-gradient(180deg, #f9f9f9, #eaeff3);

      .MuiSvgIcon-root {
        margin-right: 0;
        font-size: 18px;
        color: #768a9e;
      }
    }

    .MuiTypography-h6 {
      font-size: 15px;
      color: #083654;
      font-weight: 400;
    }
    .btn:not(:last-child) {
      margin-right: 4px;
    }
  }
}
