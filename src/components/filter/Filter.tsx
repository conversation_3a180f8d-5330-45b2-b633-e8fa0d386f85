import React from 'react'
import { Toolbar, Typography, useTheme } from '@mui/material'
import Button from '../widgets/button/Button'
import './Filter.scss'

interface FilterProps {
  className?: string
  classNameBtn?: string
  pageTitle?: string
  buttonTitle?: string
  rightIcon?: React.ReactNode
  leftIcon?: React.ReactNode
  onlyIcon?: boolean
  radius?: 'default' | 'rounded' | 'none'
  buttonStyle?: React.CSSProperties
  children?: React.ReactNode
  onClick: () => void
  disable?: boolean
}

function Filter(props: FilterProps) {
  const theme = useTheme()
  const {
    className,
    classNameBtn,
    pageTitle,
    buttonTitle,
    buttonStyle,
    rightIcon,
    leftIcon,
    radius,
    children,
    onClick,
    disable,
  } = props

  return (
    <div className={className ? `filter-panel ${className}` : 'filter-panel'}>
      <Toolbar>
        <Typography
          variant="h6"
          noWrap
          sx={{
            flex: 1,
          }}
        >
          {pageTitle}
        </Typography>

        {children}

        <Button
          buttonStyle={{ marginLeft: theme.spacing(1), ...buttonStyle }}
          title={buttonTitle}
          className={classNameBtn}
          radius={radius ? `radius-${radius}` : undefined}
          rightIcon={rightIcon}
          disable={disable}
          leftIcon={leftIcon}
          onClick={onClick}
        />
      </Toolbar>
    </div>
  )
}

Filter.defaultProps = {
  onClick: () => {},
}

export default Filter
