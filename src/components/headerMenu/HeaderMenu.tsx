import { useNavigate } from 'react-router-dom'
import { headerMenuButtons } from '@/constant/ArrayList'
import Styles from './HeaderMenu.module.scss'
import Button from '../widgets/button/Button'
import { RootState, useAppDispatch } from '@/redux/store'
import { shallowEqual, useSelector } from 'react-redux'
import { setHeaderMenu } from '@/redux/slices/AppSlice'

const HeaderMenu = () => {
  const navigate = useNavigate()
  const appDispatch = useAppDispatch()

  const activeHeaderMenu = useSelector(
    (state: RootState) => state.app.headerMenu,
    shallowEqual
  )

  return (
    <>
      <ul className={`${Styles.header_menu} d-flex justify-content-between`}>
        {headerMenuButtons.map((element: any, index: number) => (
          <li key={index}>
            <Button
              className={`${activeHeaderMenu === element.label ? `${Styles.header_btn} ${Styles.header_btn_active}` : Styles.header_btn}`}
              title={element.label}
              leftIcon={element?.icon}
              onClick={() => {
                navigate({
                  pathname: element.routePath,
                })
                appDispatch(setHeaderMenu(element.label))
              }}
            />
          </li>
        ))}
      </ul>
    </>
  )
}

export default HeaderMenu
