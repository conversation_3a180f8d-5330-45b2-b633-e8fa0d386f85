import { ReactNode } from 'react'
import ListingSkeleton from '../widgets/listingSkeleton/ListingSkeleton'
import { isMobile } from '@/utils/ViewUtils'

interface PageContainerProps {
  children: ReactNode
  loading?: boolean
  listData?: any
  className?: string
  onRefreshByHeader?: any
}
export default function PageContainer(props: PageContainerProps) {
  const { children, loading, listData, className, onRefreshByHeader } = props
  return (
    <div
      className={
        className
          ? 'page-container-wrapper ' + className
          : 'page-container-wrapper'
      }
    >
      {isMobile ? (
        loading ? (
          listData && !onRefreshByHeader ? (
            <>
              {children}
              <ListingSkeleton />
            </>
          ) : (
            <ListingSkeleton />
          )
        ) : (
          children
        )
      ) : loading ? (
        <ListingSkeleton />
      ) : (
        children
      )}
    </div>
  )
}
