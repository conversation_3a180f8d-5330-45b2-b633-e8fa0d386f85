import React from 'react'
import { Box, Typography } from '@mui/material'

interface TabPanelProps {
  children?: React.ReactNode
  index: any
  value: any
  styleName?: string
}

export function TabPanel(props: TabPanelProps) {
  const { children, value, index, styleName, ...other } = props
  return (
    <Typography
      component="div"
      className={styleName ? 'tab-table ' + styleName : 'tab-table'}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      <Box>{children}</Box>
    </Typography>
  )
}
