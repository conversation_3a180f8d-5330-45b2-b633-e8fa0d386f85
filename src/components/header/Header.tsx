import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { AppBar, Box, Toolbar } from '@mui/material'
import HeaderMenu from '@/components/headerMenu/HeaderMenu'
import Styles from './Header.module.scss'
import ProfileLogo from '@/components/header/profileLogo/profileLogo'
import { isMobile } from '@/utils/ViewUtils'
import { useAppDispatch } from '@/redux/store'
import { getUserProfileData } from '@/api/serviceActions/AppServiceActions'

const Header = () => {
  const appDispatch = useAppDispatch()
  const navigate = useNavigate()

  useEffect(() => {
    const getUserInfo = async () => {
      appDispatch(getUserProfileData())
    }
    getUserInfo()
    // eslint-disable-next-line
  }, [])

  return (
    <>
      <header className={`${Styles.header} sticky-top`}>
        <AppBar>
          <Toolbar className="d-flex justify-content-between">
            {!isMobile ? (
              <div className="d-flex align-items-center">
                <Box component="div">
                  <div
                    className={Styles.logo}
                    onClick={() => {
                      navigate('/')
                    }}
                  >
                    <img src={'/images/logo.svg'} alt="logo" />
                  </div>
                </Box>
                <HeaderMenu />
              </div>
            ) : (
              <div className="d-flex align-items-center justify-content-between w-100">
                {/* <IconButton
                  edge="start"
                  color="inherit"
                  aria-label="menu"
                  onClick={() => {
                    appDispatch(openDrawer())
                  }}
                >
                  <MenuIcon />
                </IconButton> */}
                <Box component="div">
                  <div className={Styles.mblogo} onClick={() => {}}>
                    <img src={'/images/logo.svg'} alt="logo" />
                  </div>
                </Box>
                <HeaderMenu />
              </div>
            )}
            <div className="d-flex align-items-center">
              <ProfileLogo />
            </div>
          </Toolbar>
        </AppBar>
      </header>
    </>
  )
}
export default Header
