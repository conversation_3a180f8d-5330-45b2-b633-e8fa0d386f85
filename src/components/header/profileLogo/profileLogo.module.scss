@use '../../../styles/mixins';

.profile_login {
  .profile_btn {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.12);
    border: none;
  }
  .profile_btn {
    .profile_icon {
      border-radius: 26px;
      opacity: 1;
      width: 40px;
      height: 40px;
      @include mixins.center();
      & + span {
        margin: 0 11px;
        font-size: 15px;
        text-transform: capitalize;
      }
    }
  }
}

// customized-menu
.customized_menu {
  :global .MuiMenu-paper {
    top: 64px !important;
    left: auto !important;
    right: 22px !important;
    border-radius: 4px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.3);
    min-width: 218px;
    border: 0 none;
    overflow: inherit;
    .MuiMenu-list {
      padding: 0;
      .MuiMenuItem-root {
        min-height: 48px;
        padding: 0 16px 15px;
        display: block;
        text-align: center;
        &:not(:last-child) {
          padding-top: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e4e4e4;
        }
        &:hover,
        &:focus {
          background: transparent;
        }
      }
    }
  }
  .user_info_icon {
    width: 48px;
    height: 48px;
    background: #f9f9f9;
    @include mixins.center();
    border-radius: 30px;
    margin: 0 auto;
  }
  .user_name {
    :global .MuiTypography-root {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.14;
      letter-spacing: 0.7px;
      color: #133751;
      margin: 10px 0 3px;
    }
  }
  .user_email {
    :global .MuiTypography-root {
      font-size: 13px;
      line-height: 1.15;
      letter-spacing: 0.65px;
      color: #768a9e;
      margin: 4px 0;
    }
  }
  .logout_btn,
  .logout_btn:hover,
  .logout_btn:focus {
    margin-top: 13px;
    height: 35px;
    border-radius: 4px;
    border: solid 1px #eaeff3;
    background-color: #ffffff;
    box-shadow: none;
    color: #768a9e;
    span {
      font-size: 12px;
      font-weight: 500;
      line-height: 2.08;
      color: #768a9e;
      text-transform: capitalize;
    }
    svg {
      margin-right: 10px;
      font-size: 19px;
      line-height: 1.11;
    }
  }
}

@media screen and (max-width: 767px) {
  .customized_menu {
    :global .MuiMenu-paper {
      right: 0 !important;
      left: auto !important;
      top: 49px !important;
    }
  }
  .profile_login {
    .profile_btn {
      height: 56px;
      border-radius: 0;
      margin-left: 4px;
    }
  }
}
