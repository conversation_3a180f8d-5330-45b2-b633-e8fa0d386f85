import React from 'react'
import { shallowEqual, useSelector } from 'react-redux'
import {
  ListItemIcon,
  ListItemText,
  MenuItem,
  Menu,
  MenuProps,
  MenuItemProps,
} from '@mui/material'
import { PowerSettingsNew } from '@mui/icons-material'
import { styled } from '@mui/material/styles'
import Styles from '@/components/header/profileLogo/profileLogo.module.scss'
import { selectCurrentUser } from '@/redux/slices/AppSlice'
import Button from '../../widgets/button/Button'
import { isMobile } from '@/utils/ViewUtils'

const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'center',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'center',
    }}
    {...props}
  />
))(() => ({
  paper: {
    border: '1px solid #d3d4d5',
  },
}))

const StyledMenuItem = styled((props: MenuItemProps) => (
  <MenuItem {...props} />
))(({ theme }) => ({
  root: {
    '&:focus': {
      backgroundColor: theme.palette.primary.main,
      '& .MuiListItemIcon-root, & .MuiListItemText-primary': {
        color: theme.palette.common.white,
      },
    },
  },
}))

function ProfileLogo(_props: { styleName?: string }) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const userInfo = useSelector(selectCurrentUser, shallowEqual)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <div className={Styles.profile_login}>
      <Button
        className={Styles.profile_btn}
        aria-controls="customized-menu"
        aria-haspopup="true"
        onClick={handleClick}
        leftIcon={<PowerSettingsNew />}
        title={isMobile ? '' : 'Logout'}
      />
      {/* <span className={Styles.profile_icon}>
          {' '} */}
      {/* <img src="/images/profile-icon.svg" alt="" /> */}
      {/* <PowerSettingsNew /> */}
      {/* </span> */}
      {/* <ExpandMore/> */}
      {/* </Button> */}
      <StyledMenu
        id="customized-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        className={Styles.customized_menu}
      >
        <StyledMenuItem>
          <ListItemIcon className={Styles.user_info_icon}>
            <img src="/images/profile-grey-icon.svg" alt="" />
          </ListItemIcon>
          <ListItemText
            className={Styles.user_email}
            primary={userInfo && userInfo.email}
          />
        </StyledMenuItem>

        <StyledMenuItem>
          <Button
            className={Styles.logout_btn}
            leftIcon={<PowerSettingsNew />}
            title={'Log Out'}
            onClick={() => {
              logout()
            }}
          />
        </StyledMenuItem>
      </StyledMenu>
    </div>
  )

  function logout() {
    // unRegisterServiceWorker();
    localStorage.setItem('token', '')
    const firstDot = window.location.hostname.indexOf('.')
    const redirectTo = `https://auth.${window.location.hostname.substring(firstDot === -1 ? 0 : firstDot + 1)}/logout`
    window.location.replace(redirectTo)
  }
}

export default ProfileLogo
