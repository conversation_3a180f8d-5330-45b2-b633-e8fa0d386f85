import { Component, ErrorInfo, ReactNode } from 'react'
import DataNotFound from '@/components/error/DataNotFound'

interface ErrorState {
  hasError: boolean
}

interface Props {
  children: ReactNode
}

export class ErrorBoundary extends Component<Props, ErrorState> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(_error: Error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true }
  }

  componentDidCatch(_error: Error, _errorInfo: ErrorInfo) {
    // You can also log the error to an error reporting service
    // logErrorToMyService(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return <DataNotFound />
    }

    return <>{this.props.children}</>
  }
}
