import './NotFound.scss'

interface NotFoundProps {
  header: string
  message?: string
  image?: any
  customMessage?: any
  customWarning?: any
  imageId?: any
}

function DataNotFound(props: NotFoundProps) {
  return (
    <div className="not-found data-not-found text-center">
      <div className="ContWrap">
        <div className="imgbox">
          {props.image ? (
            <img
              src={props.image}
              className="img-fluid"
              id={`${props.imageId}`}
              alt="Data Not Found"
            />
          ) : (
            <img
              src="/images/data-not-found.svg"
              className="img-fluid"
              alt="Data Not Found"
            />
          )}
        </div>
        {/* <div className="content">
                    {
                        props.customWarning ? <h4 className='red-text'>{props.customWarning}</h4> : <h4>{props.header}</h4>
                    }
                    {props.customMessage ? props.customMessage : <p>{props.message}</p>}
                </div> */}
      </div>
    </div>
  )
}

DataNotFound.defaultProps = {
  header: 'Sorry',
  message: 'What you searched was unfortunately Not found or doesn’t exist.',
}

export default DataNotFound
