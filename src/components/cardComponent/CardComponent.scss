// card css
.card-wrapper {
  &.MuiCard-root.main_card {
    .MuiCardHeader-root {
      padding-bottom: 1rem;
      margin-bottom: 1rem;
      border-bottom: solid 1px rgba(112, 112, 112, 0.1529411765);
    }
  }
  &.MuiCard-root.inner_card {
    box-shadow: none;
    border: solid 1px #e9eff4;
    border-radius: 8px;
    .MuiCardHeader-root {
      padding-bottom: 1rem;
      margin-bottom: 1rem;
      border-bottom: solid 1px #70707027;
    }
  }
  &.MuiCard-root.card-active {
    border: 1px solid #f7931e;
    border-radius: 8px;
  }
  &.MuiCard-root {
    margin-bottom: 20px;
    border: solid 1px #e9eff4;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    padding: 20px;
    overflow: inherit;
    padding-bottom: 8px;
    .MuiCardHeader-root {
      padding: 0;
      border-radius: 8px 8px 0 0;
      min-height: 40px;
      display: flex;
      align-items: center;
      .MuiCardHeader-content {
        display: flex;
        align-items: center;
        .MuiCardHeader-title,
        .MuiCardHeader-subheader {
          font-size: 16px;
          color: #143751;
          opacity: 0.86;
          font-weight: 500;
          display: flex;
          .title-value {
            margin-left: 6px;
          }
        }
        .divider {
          margin: 0 12px;
          width: 2px;
          height: 20px;
          background: #999;
        }
      }
      .MuiCardHeader-action {
        font-size: 16px;
        color: #143751;
        margin: 0;
        font-weight: 500;
        opacity: 0.86;
        .action_title {
          opacity: 0.5;
        }
        .status {
          font-weight: 500;
          margin-left: 4px;
        }
      }
    }
    .MuiCardContent-root {
      padding: 0;
      &.card-list-wrap {
        padding: 0 15px;
      }
    }
  }

  // card-table-list or error
  .card-table-list {
    padding: 16px 0px 0;
    .table-wrapper {
      min-height: auto;
      max-height: 273px;
      .MuiTable-root {
        border-spacing: 0;
      }
      .MuiTableRow-root {
        .MuiTableCell-root {
          border-bottom: none;
          border-left: none;
          border-radius: 0;
          padding-left: 20px;
          padding-right: 20px;
          border-bottom-right-radius: 8px;
          border-bottom-left-radius: 8px;
        }
        .MuiTableCell-head {
          background-color: #fff;
        }
      }
    }
  }
  .heading {
    background-color: #f8f8f8;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    .action_title,
    .status {
      font-size: 14px;
      color: #083654;
      font-weight: 500;
    }
  }
}
.card-wrapper.cardDisabled {
  background-color: #f8f8f8;
}

@media (max-width: 767px) {
  .card-wrapper {
    .card-list-wrap {
      .card-list-row {
        .left-col {
          padding-right: 0;
        }
        .right-col {
          white-space: inherit;
          text-overflow: inherit;
          word-break: break-word;
        }
      }
    }
    &.MuiCard-root {
      padding: 12px;
      .MuiCardHeader-root {
        .MuiCardHeader-content {
          .MuiCardHeader-title,
          .MuiCardHeader-subheader {
            flex-direction: column;
            color: #083654;
            opacity: 1;
            .title-label {
              font-size: 13px;
              // opacity: 0.9;
              display: block;
              width: 100%;
            }
            .title-value {
              font-size: 14px;
              margin: 0;
            }
          }
          .MuiCardHeader-subheader {
            flex-direction: row;
          }
          .divider {
            margin: 0 8px;
            height: 40px;
            background: #ddd;
          }
        }
        .MuiCardHeader-action {
          display: flex;
          flex-direction: column;
          .action_title {
            font-size: 13px;
          }
          .status {
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
    &.MuiCard-root.inner_card {
      padding-top: 2px;
      .MuiCardHeader-root {
        padding-bottom: 0px;
        .MuiCardHeader-title {
          font-size: 14px;
        }
      }
    }
    .mb-card-head {
      background-color: #eaeff3af;
      margin: -2px -12px 12px;
      padding: 4px 16px;
      border-radius: 4px 4px 0px 0px;
      .flex-grow-1 {
        display: flex;
        .info-heading {
          font-size: 12px;
          margin: 0;
        }
        .vehicle-info {
          font-size: 14px;
          margin-left: 6px;
          font-weight: 500;
          .media-body {
            max-width: fit-content;
          }
        }
      }
    }
  }
}
