import { isNullValue } from '@/utils/StringUtils'
import { Card, CardActions, CardContent, CardHeader } from '@mui/material'
import './CardComponent.scss'

export interface CardComponentProps {
  title?: React.ReactNode
  subheader?: React.ReactNode
  heading?: any
  headAction?: any
  children: any
  cardFooter?: any
  styleName?: string
  disableSubmit?: boolean
  action?: React.ReactNode
  cardHeaderStyle?: string
  showSubmitLoader?: boolean
}

function CardComponent(props: CardComponentProps) {
  const {
    title,
    subheader,
    headAction,
    children,
    cardFooter,
    styleName,
    heading,
    action,
    cardHeaderStyle,
  } = props

  return (
    <>
      <Card
        className={`card-wrapper ${!isNullValue(styleName) ? styleName : ''}`}
      >
        {heading}
        {title && (
          <CardHeader
            className={cardHeaderStyle || ''}
            title={title}
            subheader={subheader}
            action={headAction}
          />
        )}
        <CardContent>{children}</CardContent>

        {cardFooter && <CardActions>{cardFooter}</CardActions>}
      </Card>

      {action && (
        <div className="d-flex align-items-center justify-content-end mt-2">
          {action}
        </div>
      )}
    </>
  )
}

export default CardComponent
