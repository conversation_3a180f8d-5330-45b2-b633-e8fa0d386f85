.uploadFileWrap {
  .uploadFile {
    // margin-bottom: 16px;
    .uploadLabel {
      white-space: nowrap;
      label {
        font-size: 12px;
        color: #2c2c2c;
        margin-bottom: 4px;
      }
      .mandatoryFlied {
        color: red;
        margin-left: 4px;
      }
    }
    .uploadInput {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #143751;
      font-size: 15px;
      padding: 0 6px;
      height: 44px;
      border-radius: 3px;
      box-sizing: border-box;
      border: solid 2px #e0e4e6;
      input {
        &[type='file'] {
          color: #404040d5;
          font-size: 14px;
          opacity: 0.92;
          cursor: pointer;
        }
        &::file-selector-button {
          height: 32px;
          font-size: 13px;
          color: #2f2f2f;
          padding: 0.5em 0.8em;
          border: thin solid #70707039;
          border-radius: 4px;
          background-color: #fff;
          margin-left: 8px;
          cursor: pointer;
        }
      }
      .cirProgIcon {
        margin: 0;
        width: 24px !important;
        height: 24px !important;
      }
      .closeWrap {
        margin-right: 8px;
        position: relative;
        .uploadImg {
          width: 24px;
          height: 24px;
        }
        .closeIcon {
          color: #ffffff;
          background-color: #f48300;
          position: absolute;
          padding: 2px;
          border-radius: 50%;
          font-size: 18px;
          top: -6px;
          left: 14px;
          cursor: pointer;
        }
      }
    }
    .uploadInput:has(input:disabled) {
      background-color: #f9f9f9;
    }
    .uploadInput:has(:global #imgDisabled) {
      filter: grayscale(100%);
      svg {
        cursor: default;
      }
    }
    .uploadInput:has(:global #imgEnable) {
      filter: grayscale(0%);
      svg {
        cursor: pointer;
      }
    }
  }
  .uploadVariant {
    display: flex;
    align-items: center;
    .uploadLabel {
      white-space: nowrap;
      align-items: center;
      justify-content: end;
      display: flex;
      min-width: 150px;
      label {
        font-size: 14px;
        margin-right: 16px;
      }
    }
    .uploadInput {
      width: 100%;
      height: 48px;
      padding: 0 16px;
      background-color: #f7f7f7;
      border: dotted 0.16em #d4d4d4;
      display: flex;
      align-items: center;
      input {
        &::file-selector-button {
          margin-right: 12px;
        }
      }
    }
  }
}

@media (max-width: 767px) {
  .uploadInput {
    input {
      width: 100%;
    }
  }
}
