import React from 'react'
import Styles from './FileUpload.module.scss'
import { Close } from '@mui/icons-material'

interface FileUploadProps {
  label?: string
  styleName?: string
  labelStyle?: string
  variant?: string
  appDispatch?: (action: unknown) => void
  uploadDocuments?: any
  setUploadDocuments?: (action: unknown) => void
  mandatory?: boolean
  fileType?: string
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  refCallBack?: (ref: HTMLInputElement | null) => void
  showUploadedImgIcon?: boolean
  disabled?: boolean
  onUploadedFileDelete?: () => void
  isImgDisabled?: boolean
}

function FileUpload(props: FileUploadProps) {
  const {
    label,
    styleName,
    labelStyle,
    variant,
    mandatory,
    fileType,
    onChange,
    refCallBack,
    showUploadedImgIcon = false,
    disabled = false,
    onUploadedFileDelete,
    isImgDisabled = false,
  } = props
  return (
    <div className={`${Styles.uploadFileWrap}  ${styleName || ''}`}>
      <div className={`${Styles.uploadFile} ${variant && Styles[variant]}`}>
        <div className={`${Styles.uploadLabel} ${labelStyle || ''}`}>
          <label>
            {label}
            {mandatory && <span className={Styles.mandatoryFlied}>*</span>}
          </label>
        </div>
        <div className={Styles.uploadInput}>
          <input
            type="file"
            accept={fileType}
            // style={{ display: 'none' }}
            id={`upload-file`}
            onChange={(event: any) => {
              onChange(event)
            }}
            ref={refCallBack}
            disabled={disabled}
          />
          {showUploadedImgIcon && (
            <div
              className={Styles.closeWrap}
              id={isImgDisabled ? 'imgDisabled' : 'imgEnable'}
            >
              <img
                className={Styles.uploadImg}
                src="/images/upload-img.svg"
                alt={'Uploaded pic thumbnail '}
                title={'Upload Image'}
              />
              <Close
                className={Styles.closeIcon}
                onClick={() => !isImgDisabled && onUploadedFileDelete?.()}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default FileUpload
