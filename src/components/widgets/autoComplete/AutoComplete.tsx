import React, { useEffect, useRef, useState } from 'react'
import Select, { createFilter } from 'react-select'
import { components } from 'react-select'
import './AutoComplete.scss'
import { OptionType } from '../widgetsInterfaces'

const { Option, SingleValue, MultiValue } = components

interface AutoCompleteProps {
  placeHolder: string
  label?: string | React.ReactNode
  error?: string
  isLoading?: boolean
  onChange: (value: any) => void
  options: OptionType[] | undefined
  value: any
  icon?: any
  renderOption?: any
  renderValueHolder?: any
  showCustomView?: boolean
  defaultValue?: any
  isDisabled?: boolean
  mandatory?: boolean
  name?: string
  toolTip?: () => React.ReactNode
  isClearable?: boolean
  isShowAll?: boolean
  menuPortalTarget?: any
  labelStyle?: any
  styleName?: string
  onMenuOpen?: any
  isMulti?: boolean
}

export default function AutoComplete(props: AutoCompleteProps) {
  const {
    error,
    mandatory,
    isClearable,
    isShowAll,
    menuPortalTarget,
    labelStyle,
    onMenuOpen,
    isMulti = false,
  } = props
  const [options, setOptions] = useState<any>()
  useEffect(() => {
    if ((props.options && props.options.length <= 10) || isShowAll) {
      setOptions(props.options)
    } else if (props.options && props.options.length > 10) {
      setOptions(props.options.slice(0, 10))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setOptions, props.options])

  function handleChangeSingle(value: any) {
    if (myInput.current) myInput.current.blur()
    props.onChange(value)
  }

  // const selectStyles = {
  //     input: (base: CSSProperties) => ({
  //         ...base,
  //         color: theme.palette.text.primary,
  //         '& input': {
  //             font: 'inherit',
  //         },
  //     }),
  // };

  const customOption = (optionProps: any) => {
    return (
      <Option {...optionProps}>
        {(props.showCustomView &&
          props.renderOption &&
          props.renderOption(optionProps.data)) ||
          optionProps.data.label}
      </Option>
    )
  }

  const customSingleValue = (valueProps: any) => {
    return (
      <SingleValue {...valueProps}>
        {(props.showCustomView &&
          props.renderValueHolder &&
          props.renderValueHolder(valueProps)) ||
          valueProps.children}
      </SingleValue>
    )
  }
  const customMultiValue = (valueProps: any) => {
    return (
      <MultiValue {...valueProps}>
        {(props.showCustomView &&
          props.renderValueHolder &&
          props.renderValueHolder(valueProps)) ||
          valueProps.children}
      </MultiValue>
    )
  }

  const myInput = useRef<any>(null)
  useEffect(() => {
    if (error && myInput.current) {
      myInput.current.focus()
    }
  }, [error])

  return (
    <div className={`${props.styleName || ''} autocomplete-wrap`}>
      {props.label && (
        <label
          htmlFor=""
          className={labelStyle ? labelStyle : 'd-flex align-items-center'}
        >
          {props.label}
          {mandatory && <span className="mandatory-flied">*</span>}
          <span>{props.toolTip && props.toolTip()}</span>
        </label>
      )}
      <Select
        menuPlacement="auto"
        ref={myInput}
        className="select-container"
        menuPortalTarget={menuPortalTarget}
        // styles={selectStyles}
        // inputId="react-select"
        isClearable={isClearable}
        isMulti={isMulti}
        classNamePrefix="select"
        filterOption={createFilter({
          ignoreAccents: false,
          ignoreCase: true,
        })}
        isLoading={props.isLoading}
        isSearchable={true}
        backspaceRemovesValue={true}
        placeholder={props.placeHolder}
        options={options}
        name={props.name || ''}
        isDisabled={props.isDisabled}
        components={{
          Option: customOption,
          SingleValue: customSingleValue,
          MultiValue: customMultiValue,
        }}
        defaultValue={props.defaultValue}
        value={(props.value && props.value) || null}
        onInputChange={(newValue) => {
          const optionList = isShowAll
            ? props.options &&
              props.options.filter((element: any) => {
                try {
                  return element.label
                    .toLocaleLowerCase()
                    .includes(newValue.toLocaleLowerCase())
                } catch (error) {
                  return element
                }
              })
            : props.options &&
              props.options
                .filter((element: any) => {
                  try {
                    return element.label
                      .toLocaleLowerCase()
                      .includes(newValue.toLocaleLowerCase())
                  } catch (error) {
                    return element
                  }
                })
                .slice(0, 10)
          setOptions(optionList)
        }}
        onChange={handleChangeSingle}
        onMenuOpen={onMenuOpen}
      />
      {error && <label className="error">{error}</label>}
    </div>
  )
}
