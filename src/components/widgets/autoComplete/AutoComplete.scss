.select-container {
  .select__control {
    border-radius: 4px;
    border: solid 2px #e0e4e6;
    min-height: 44px;
    padding: 0 4px;

    &:hover {
      border: solid 2px #ebeff3;
    }

    &.select__control--is-disabled {
      background-color: #f9f9f9 !important;
    }
  }

  .select__placeholder {
    font-size: 14px;
    line-height: 1.17;
    font-weight: 400;
    color: #acb6c0;
  }

  /* .select__dropdown-indicator {
      color: rgba(0, 108, 201, 0.8);
  } */

  &.select--is-disabled {
    .select__dropdown-indicator {
      color: hsl(0, 0%, 80%);
    }
  }

  .select__single-value {
    color: #121212;
    font-size: 14px;
    width: 100%;

    > span {
      white-space: break-spaces;

      @media (min-width: 768px) {
        margin-right: 10px;
        max-width: 350px;
      }
    }

    @media (min-width: 768px) {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .chips.MuiChip-root {
      @media (max-width: 767px) {
        margin: 4px 8px 4px 0;
      }
    }
  }

  .select__menu {
    z-index: 99;

    .select__option.select__option--is-selected {
      background-color: rgb(66, 139, 202, 0.9);
      color: #fff;
    }
    .select__option {
      word-break: break-all;
      background-color: rgb(66, 139, 202, 0.09);
      &:hover {
        color: #fff;
        background-color: rgb(66, 139, 202, 0.9);
        .MuiChip-root {
          background: #fff;
        }
      }
      > span {
        white-space: break-spaces;

        @media (min-width: 768px) {
          margin-right: 10px;
          max-width: 350px;
        }
      }

      @media (min-width: 768px) {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &--is-selected {
        .chips.MuiChip-root {
          background-color: #fff;
        }
      }
    }
  }

  .menu-options {
    .menu-options-value {
      padding: 0 10px;
    }
  }

  .badge {
    color: #083654;
    background-color: #eaeff3;
    font-size: 12px;
    font-weight: 500;
  }
}

.autocomplete-wrap {
  position: relative;

  label {
    font-size: 12px;
    color: #143751;
    margin-bottom: 4px;

    .mandatory-flied {
      color: red;
      margin-left: 4px;
    }
  }
}

.standardAutoComplete {
  &.autoCompleteDisabled {
    background-color: #f9f9f9;
    padding: 4px 0px 0 4px;

    label,
    .select-container .select__control {
      opacity: 0.7;
    }
  }

  label {
    font-size: 12px;
    margin-bottom: 0;
    color: #434343;
    line-height: normal;
    font-weight: 500;
  }

  .select-container {
    .select__control {
      border: none;
      border-radius: 0;
      padding: 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.42);
      background-color: transparent;
      box-shadow: none;
      min-height: 32px;
    }

    .select__dropdown-indicator {
      color: #acb6c0;
    }
  }

  .select__value-container,
  .select__input-container {
    margin: 0;
    padding: 0;
  }

  .select__indicator {
    padding: 0 8px;
  }
}

.autocomplete-wrap {
  .select__multi-value {
    background-color: #fff;
    border-radius: 16px;
    padding: 5px 8px;
    line-height: normal;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    border: 1px solid #ddd;
    margin: 0;
    margin-right: 4px;
  }
  .select__multi-value__label {
    padding: 0 4px;
    margin-right: 4px;
    font-size: 12px;
  }
  .select__multi-value__remove {
    cursor: pointer;
    color: #ffffff;
    background-color: #bebebe;
    padding: 2px;
    border-radius: 50%;
    &:hover {
      color: #000;
    }
  }
  .select-container {
    border-radius: 8px;
    background-color: #fafafa;
  }
}
