import { useCallback, useEffect, useState } from 'react'
import {
  <PERSON>per,
  Paper,
  MenuItem,
  TextField,
  Box,
  Typography,
} from '@mui/material'
import _ from 'lodash'
import Autosuggest from 'react-autosuggest'
import './AutoSuggest.scss'

interface OptionType {
  label: string
}

interface AutosuggestProps {
  placeHolder: string
  label?: string
  error?: string
  isLoading?: boolean
  onChange: (value: any) => void
  onSelected: (suggestion: any) => void
  suggestions?: OptionType[]
  value: string | undefined
  icon?: any
  renderOption?: any
  renderValueHolder?: any
  showCustomView?: boolean
  isDisabled?: boolean
  mandatory?: boolean
  maxLength?: number
  toolTip?: () => React.ReactNode
  handleSuggestionsFetchRequested?: any
  isClearable?: boolean
  onClear?: any
  labelStyle?: any
}

function renderInputComponent(inputProps: any) {
  const { inputRef = () => {}, ref, error, ...other } = inputProps

  return (
    <TextField
      fullWidth
      helperText={error || ''}
      inputRef={(node) => {
        ref(node)
        inputRef(node)
      }}
      {...other}
    />
  )
}

function getSuggestionValue(suggestion: OptionType) {
  return suggestion.label
}

export default function AutoSuggest(props: AutosuggestProps) {
  const {
    value,
    suggestions,
    handleSuggestionsFetchRequested,
    error,
    isDisabled,
    mandatory,
    maxLength,
    renderOption,
    isClearable,
    onClear,
    labelStyle,
  } = props

  const [anchorEl, setAnchorEl] = useState<any>(null)
  const [stateSuggestions, setSuggestions] = useState<OptionType[]>([])

  useEffect(() => {
    setSuggestions(suggestions ?? [])
  }, [suggestions])

  const onSuggestionSelected = (event: any, { suggestion }: any) => {
    event.preventDefault()
    props.onSelected(suggestion)
  }

  const handleSuggestionsClearRequested = () => {
    setSuggestions([])
  }

  const handleChange =
    (_name: any) =>
    // eslint-disable-next-line @typescript-eslint/no-empty-object-type
    (_event: React.ChangeEvent<{}>, { newValue }: Autosuggest.ChangeEvent) => {
      if (maxLength && maxLength > 0) {
        if (newValue.length < maxLength) {
          props.onChange(newValue)
        }
      } else {
        props.onChange(newValue)
      }
    }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const suggestionsFetchRequested = useCallback(
    _.debounce(({ value }: any) => {
      if (handleSuggestionsFetchRequested) {
        handleSuggestionsFetchRequested({ value })
      } else {
        setSuggestions(getSuggestions(value))
      }
    }, 300),
    []
  )

  function renderDefaultSuggestion(optionProps: any) {
    return (
      <Box
        display="flex"
        alignItems="center"
        className="menu-options lane-wrap-text"
      >
        <Typography className="menu-options-item word-wrap">
          {optionProps.label}
        </Typography>
      </Box>
    )
  }

  function renderSuggestion(
    suggestion: OptionType,
    _params: Autosuggest.RenderSuggestionParams
  ) {
    return (
      <MenuItem component="div" id="wrapp-text">
        {renderOption
          ? renderOption(suggestion)
          : renderDefaultSuggestion(suggestion)}
      </MenuItem>
    )
  }

  const autosuggestProps = {
    renderInputComponent,
    suggestions: stateSuggestions,
    onSuggestionsFetchRequested: suggestionsFetchRequested,
    onSuggestionsClearRequested: handleSuggestionsClearRequested,
    getSuggestionValue,
    renderSuggestion,
  }

  useEffect(() => {
    if (error && anchorEl) {
      anchorEl.focus()
    }
  }, [error, anchorEl])

  return (
    <Box className="autosuggest-wrap">
      <Box display="flex" justifyContent="space-between">
        <label className={labelStyle ?? 'd-flex align-items-center'}>
          {props.label}
          {mandatory && <span className="mandatory-flied">*</span>}
          <span>{props.toolTip && props.toolTip()}</span>
        </label>
        {isClearable && (
          <Typography
            variant="body2"
            className="clear-label orange-text"
            onClick={onClear}
            sx={{ cursor: 'pointer' }}
          >
            Clear
          </Typography>
        )}
      </Box>

      <Autosuggest
        ref={storeInputReference}
        {...autosuggestProps}
        inputProps={{
          placeholder: props.placeHolder,
          disabled: isDisabled,
          value: value || '',
          onChange: handleChange('popper'),
        }}
        onSuggestionSelected={onSuggestionSelected}
        theme={{
          suggestionsList: {
            margin: 0,
            padding: 0,
            listStyleType: 'none',
            whiteSpace: 'inherit',
            maxHeight: 300,
            overflowX: 'hidden',
          },
          suggestion: { display: 'block' },
        }}
        renderSuggestionsContainer={(options: any) => (
          <Popper
            id="word-br"
            {...options.containerProps}
            sx={{ zIndex: 9999, whiteSpace: 'inherit' }}
            anchorEl={anchorEl}
            open={Boolean(options.children)}
          >
            <Paper
              square
              {...options.containerProps}
              sx={{ width: anchorEl ? anchorEl.clientWidth : undefined }}
            >
              {options.children}
            </Paper>
          </Popper>
        )}
      />
      {error && <label className="error">{error}</label>}
    </Box>
  )

  function storeInputReference(autosuggest: any) {
    if (autosuggest !== null) {
      setAnchorEl(autosuggest.input)
    }
  }

  function getSuggestions(value: string) {
    const inputValue = value.trim().toLowerCase()
    const inputLength = inputValue.length
    let count = 0

    return inputLength === 0
      ? []
      : (props.suggestions &&
          props.suggestions.filter((suggestion) => {
            const keep =
              suggestions && suggestions.length > 5
                ? count < 5 &&
                  suggestion.label.slice(0, inputLength).toLowerCase() ===
                    inputValue
                : suggestions

            if (keep) {
              count += 1
            }

            return keep
          })) ||
          []
  }
}
