.autosuggest-wrap {
  label {
    font-size: 12px;
    color: #143751;
    font-weight: 500;    
  }
  
  .mandatory-flied {
    color: red;
    margin-left: 4px;
  }
  
  .MuiInputBase-input {
    color: #143751;
    font-size: 15px;
    padding-top: 0;
    padding-bottom: 0;
    height: 44px;  
    border: solid 2px #E0E4E6;
  }
}

.autosuggest-with-btn {
  position: relative;
  
  .action-btn {
    position: absolute;
    right: 6px;
    bottom: 6px;
    margin: auto;
    
    .verified {
      width: 22px;
      color: #1FC900;
      margin-top: -16px;
      margin-right: 16px;
    }
  }
  
  .add-btn {
    height: 34px;
    border-radius: 30px;
    font-size: 14px;
  }
  
  .MuiOutlinedInput-notchedOutline {
    border: none;
  }
  
  .MuiInputBase-input {
    padding-right: 70px;
  }
}

.clear-label {
  cursor: pointer;
}

.center-align {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.menu-options {
  .menu-options-item {
    white-space: normal;
  }
}