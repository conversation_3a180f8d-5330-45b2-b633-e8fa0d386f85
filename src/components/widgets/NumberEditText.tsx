import React, { useEffect, useRef } from 'react'
import NumberFormat from 'react-number-format'
import { TextField } from '@mui/material'
import './editText/EditText.scss'

interface EditTextProps {
  label?: string | React.ReactNode
  placeholder: string
  value: string | number | undefined
  onChange: (value: any) => void
  validator?: RegExp | undefined
  maxLength: number
  type?: string
  name?: string
  error?: any
  required?: boolean
  toolTip?: () => React.ReactNode
  allowNegative?: boolean
  decimalScale?: number
  disabled?: boolean
  decimalSeparator?: boolean
  mandatory?: boolean
  onBlur?: any
  allowZero?: boolean
  variant?: 'filled' | 'outlined' | 'standard'
  className?: string
  endAdornment?: any
  styleName?: string
  labelCheckBox?: any
}

interface NumberFormatCustomProps {
  inputRef: (instance: NumberFormat<any> | null) => void
  onChange: (event: { target: { value: string } }) => void
  maxLength: number
  allowNegative?: boolean
  decimalScale?: number
  decimalSeparator?: string
  allowZero?: boolean
}

function NumberFormatCustom(props: NumberFormatCustomProps) {
  const {
    inputRef,
    onChange,
    maxLength,
    allowNegative,
    decimalScale,
    decimalSeparator,
    allowZero,
    ...other
  } = props
  return (
    <NumberFormat
      {...other}
      getInputRef={inputRef}
      onValueChange={(values: any) => {
        if (allowZero) {
          onChange({
            target: {
              value: values.floatValue
                ? values.floatValue + ''
                : values.floatValue === 0
                  ? values.floatValue + ''
                  : '',
            },
          })
        } else {
          onChange({
            target: {
              value: values.floatValue ? values.floatValue + '' : '',
            },
          })
        }
      }}
      decimalScale={decimalScale}
      decimalSeparator={decimalSeparator}
      isAllowed={(values: any) => {
        const { value } = values
        return value === '' || value.length <= maxLength
      }}
      allowNegative={allowNegative}
      isNumericString
    />
  )
}

const NumberEditText = (props: EditTextProps) => {
  const {
    allowNegative = false,
    maxLength,
    decimalScale,
    disabled,
    error,
    decimalSeparator,
    mandatory,
    allowZero,
    onBlur,
    endAdornment,
  } = props
  const myInput = useRef<any>(null)
  useEffect(() => {
    if (
      error &&
      myInput.current &&
      typeof myInput.current.focus === 'function'
    ) {
      myInput.current.focus()
    }
  }, [error])
  return (
    <div className={`inputWrap ${props.styleName ? props.styleName : ''}`}>
      <label className="d-flex align-items-center justify-content-between">
        <span>
          {props.label && props.label}
          {mandatory && <span className="mandatory-flied text-danger"> *</span>}
        </span>
        {props.toolTip && <span>{props.toolTip && props.toolTip()}</span>}
        {props.labelCheckBox && (
          <span className="label_Checkbox">{props.labelCheckBox}</span>
        )}
      </label>
      <TextField
        inputRef={myInput}
        placeholder={props.placeholder}
        variant={props.variant}
        className={`${props.className} ${props.variant}`}
        value={props.value || ''}
        // required={props.required || false}
        disabled={disabled}
        helperText={error || ''}
        onBlur={() => {
          if (onBlur) onBlur()
        }}
        onChange={(event: any) => {
          props.onChange(event.target.value)
        }}
        InputProps={{
          inputComponent: NumberFormatCustom as any,
          inputProps: {
            maxLength: maxLength,
            allowNegative: allowNegative,
            decimalScale: decimalScale,
            decimalSeparator: decimalSeparator,
            allowZero: allowZero,
            inputMode: 'decimal',
          },
          endAdornment: endAdornment,
        }}
      />
    </div>
  )
}

export default NumberEditText
