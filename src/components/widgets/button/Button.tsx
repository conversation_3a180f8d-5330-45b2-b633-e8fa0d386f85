import React from 'react'
import './Button.scss'
import CircularProgress from '@mui/material/CircularProgress'
import Tooltip from '@mui/material/Tooltip'

// Interface for tooltip styling
interface TooltipStyleProps {
  backgroundColor?: string
  textColor?: string
  fontSize?: string | number
  padding?: string | number
  maxWidth?: string | number
  borderRadius?: string | number
  boxShadow?: string
  arrowColor?: string
  fontWeight?: string | number
}

// Interface for tooltip configuration
interface TooltipConfig {
  show: boolean
  text: string
  placement?: 'top' | 'bottom' | 'left' | 'right'
  styles?: TooltipStyleProps
}

export interface ButtonProps {
  title?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  buttonStyle?: React.CSSProperties
  onClick?: React.MouseEventHandler<HTMLButtonElement>
  type?: 'button' | 'submit' | 'reset' | undefined
  loading?: boolean
  primaryButton?: boolean
  count?: number
  disable?: boolean
  customView?: React.ReactNode
  titleStyle?: string
  leftIconStyle?: string
  rightIconStyle?: string
  height?: number
  size?: 'btn-default' | 'btn-sm' | 'btn-lg' | 'btn-xl'
  radius?: 'radius-default' | 'radius-rounded' | 'radius-none'
  fullWidth?: boolean
  minWidth?: number
  className?: string
  hideOnMobile?: boolean

  // Button tooltip props
  showTooltip?: boolean
  tooltipText?: string
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  tooltipStyles?: TooltipStyleProps

  // Individual element tooltips
  leftIconTooltip?: TooltipConfig
  rightIconTooltip?: TooltipConfig
  titleTooltip?: TooltipConfig
}

export default function Button(props: ButtonProps) {
  const {
    loading,
    count = 0,
    disable,
    leftIcon,
    rightIcon,
    title,
    buttonStyle,
    onClick,
    type = 'button',
    customView,
    titleStyle,
    leftIconStyle,
    rightIconStyle,
    height,

    // New props with defaults
    size,
    radius = '',
    fullWidth = false,
    minWidth,
    className = '',
    hideOnMobile = false,

    // Button tooltip props
    showTooltip = false,
    tooltipText = '',
    tooltipPlacement = 'top',
    tooltipStyles = {},

    // Individual element tooltips
    leftIconTooltip,
    rightIconTooltip,
    titleTooltip,
  } = props

  const buttonClasses = [
    'btn',
    size,
    radius,
    hideOnMobile && 'btn-hide',
    className,
  ]
    .filter(Boolean)
    .join(' ')

  const buttonStyles = {
    ...buttonStyle,
    ...(fullWidth && { width: '100%' }),
    ...(minWidth && { minWidth: `${minWidth}px` }),
    ...(height !== undefined && { height: `${height}px` }),
  }

  // Helper function to create styled tooltip using MUI v6 approach
  const getStyledTooltipProps = (styles: TooltipStyleProps = {}) => {
    return {
      componentsProps: {
        tooltip: {
          sx: {
            bgcolor: styles.backgroundColor || '#ffffff !important',
            color: styles.textColor || '#143751',
            fontSize: styles.fontSize || '12px !important',
            padding: styles.padding || '4px 8px !important',
            maxWidth: styles.maxWidth || 240,
            borderRadius: styles.borderRadius || '4px !important',
            boxShadow:
              styles.boxShadow || '0px 2px 4px rgba(0,0,0,0.2) !important',
            fontWeight: styles.fontWeight || 'normal',
          },
        },
        arrow: {
          sx: {
            color:
              styles.arrowColor ||
              styles.backgroundColor ||
              '#ffffff !important',
          },
        },
      },
      // Enable touch support for mobile devices
      disableFocusListener: false,
      disableTouchListener: false,
      disableHoverListener: false,
      enterTouchDelay: 50,
      leaveTouchDelay: 1500,
    }
  }

  // Helper function to conditionally wrap element with styled tooltip
  const withTooltip = (
    element: React.ReactNode,
    tooltipConfig?: TooltipConfig
  ) => {
    if (tooltipConfig?.show) {
      const tooltipProps = getStyledTooltipProps(tooltipConfig.styles)

      return (
        <Tooltip
          title={tooltipConfig.text}
          placement={tooltipConfig.placement || 'top'}
          arrow
          {...tooltipProps}
        >
          {/* Tooltip requires a DOM element that can receive focus as its child */}
          <span style={{ display: 'inline-flex' }}>{element}</span>
        </Tooltip>
      )
    }
    return element
  }

  // Render the button content with individual tooltips
  const renderButtonContent = () => {
    if (customView) {
      return customView
    } else {
      return (
        <>
          {leftIcon && (
            <span
              className={`${title ? 'me-2' : ''} left-icon ${leftIconStyle || ''}`}
            >
              {withTooltip(leftIcon, leftIconTooltip)}
            </span>
          )}

          {count > 0 && <span className="mr-2 count-text">{count}</span>}

          {title && (
            <span className={`title ${titleStyle || ''}`}>
              {withTooltip(title, titleTooltip)}
            </span>
          )}

          {rightIcon && (
            <span
              className={`${title ? 'ms-2' : ''} right-icon ${rightIconStyle || ''}`}
            >
              {withTooltip(rightIcon, rightIconTooltip)}
            </span>
          )}
        </>
      )
    }
  }

  // Render loading spinner if loading is true
  const loadingSpinner = loading && (
    <CircularProgress
      size={24}
      sx={{
        color: '#006cc9',
        position: 'absolute',
        top: '50%',
        left: '50%',
        marginTop: '-12px',
        marginLeft: '-12px',
      }}
    />
  )

  const buttonElement = (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={loading || disable}
      style={buttonStyles}
    >
      {renderButtonContent()}
      {loadingSpinner}
    </button>
  )

  // Apply tooltip to entire button if showTooltip is true
  if (showTooltip) {
    const buttonTooltipProps = getStyledTooltipProps(tooltipStyles)

    return (
      <Tooltip
        title={tooltipText}
        placement={tooltipPlacement}
        arrow
        {...buttonTooltipProps}
      >
        {buttonElement}
      </Tooltip>
    )
  }

  return buttonElement
}
