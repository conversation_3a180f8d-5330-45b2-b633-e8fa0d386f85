/* Button base styles */
.btn {
  position: relative;
  transition: 0.5s all !important;
  -webkit-transition: 0.5s all !important;
  font-size: var(--btn-font-size);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  // min-width: 105px;

  &:focus {
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1) !important;
    outline: none;
  }
  .title {
    line-height: normal;
  }
  .MuiSvgIcon-root {
    font-size: 18px;
  }
  img {
    max-width: 18px;
    max-height: 18px;
    width: auto;
    height: auto;
  }

  .left-icon,
  .right-icon {
    display: flex;
    align-items: center;
  }

  .count-text {
    margin-right: 8px;
  }
}
.radius-default {
  border-radius: 3px;
}
.radius-rounded {
  border-radius: 50%;
}
.radius-none {
  border-radius: 0;
}

.btn-sm {
  font-size: var(--btn-sm-font-size);
  height: 32px;
}
.btn-warning {
  color: #ffffff;
  background: #ff8900;
  border-color: #ff8900;
  transition: 0.5s all;
  &:hover {
    opacity: 0.8;
    color: #ffffff;
    background: #ff8900;
    border-color: #ff8900;
  }
}

.btn-warning-light {
  color: #434343;
  background: #f7a41e26;
  border-color: #dd840a45;
  transition: 0.5s all;
  &:hover {
    opacity: 0.8;
    color: #434343;
    background: #f7a41e26;
    border-color: #dd840a45;
  }
}

.btn-orange {
  color: #fff;
  background-color: #f7931e;
  background: -webkit-linear-gradient(to left, #f73f1e, #f7931e, #f73f1e);
  background: linear-gradient(to left, #f73f1e, #f7931e, #f73f1e);
  background-size: 200% auto;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);

  &:hover {
    color: #fff;
    background-position: right center;
  }
}

.btn-blue {
  background-color: #006cc9;
  background-image: -webkit-linear-gradient(to left, #006cc9, #145288, #006cc9);
  background-image: linear-gradient(to left, #006cc9, #145288, #006cc9);
  background-size: 200% auto;
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
  color: #fff;

  &:hover {
    color: #fff;
    background-position: right center;
  }

  &:disabled {
    background-color: #006cc9;
    background-image: -webkit-linear-gradient(
      to left,
      #006cc9,
      #145288,
      #006cc9
    );
    background-image: linear-gradient(to left, #006cc9, #145288, #006cc9);
    background-size: 200% auto;
    color: #fff;
    opacity: 0.7;
  }
}

.btn-white {
  background-color: #fff;
  color: rgba(60, 60, 60, 0.86);
  border: 1px solid #eaeff3;

  &:hover,
  &:focus,
  &:first-child:active,
  &:disabled {
    background-color: #fff;
    color: rgba(60, 60, 60, 0.86);
  }
  svg {
    color: #ff9119;
  }
}

.btn-close {
  background: transparent;
  color: rgba(60, 60, 60, 0.86);
  border: none;
  width: 2em;
  height: 2em;
  opacity: 1;
  border-radius: 50%;

  &:hover,
  &:focus,
  &:first-child:active,
  &:disabled {
    background-color: rgba(0, 0, 0, 0.04);
    color: rgba(60, 60, 60, 0.86);
  }
  svg {
    color: rgba(60, 60, 60, 1);
  }
}

.btn-cancel {
  background: transparent linear-gradient(270deg, #f9f9f9 0%, #eaeff3 100%) 0%
    0% no-repeat padding-box;
  color: #143751;
  box-shadow: none;
  border: solid 1px #eaeff3;

  &:hover {
    color: #143751;
  }

  svg {
    font-size: 14px;
  }
}

.btn-grey {
  background: transparent linear-gradient(270deg, #f9f9f9 0%, #eaeff3 100%) 0%
    0% no-repeat padding-box;
  color: #143751;
  box-shadow: none;
  border: solid 1px #eaeff3;

  &:hover {
    color: #143751;
  }

  svg {
    font-size: 14px;
  }
}

.btn-grey-cancel {
  background: linear-gradient(270deg, #f0efef, #dfe5ea);
  background: -webkit-linear-gradient(270deg, #f0efef, #dfe5ea);
  color: #6a7a8b;
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    color: #000000;
  }
}

.btn-red-reject {
  background: linear-gradient(267deg, #fd1d00, #c31d07);
  background: -webkit-linear-gradient(267deg, #fd1d00, #c31d07);
  color: #ffffff;

  &:hover {
    color: #ffffff;
  }
}

.btn-detail {
  min-width: 80px;
  background-color: #f9f9f9;
  background-size: 200% auto;
  height: auto;
  background-image: linear-gradient(to bottom, #f9f9f9, #eaeff3);
  color: #133751;

  &:hover {
    color: #fff;
    background-color: #f7931e;
    background: -webkit-linear-gradient(to right, #f7931e, #de5811, #f7931e);
    background: linear-gradient(to right, #f7931e, #de5811, #f7931e);
    background-size: 200% auto;
    background-position: right center;
  }
}
/* 
.btn.sml-btn {
  border-radius: 50px;
  min-width: 72px;
  font-size: 11px;
  height: 27px;

  svg {
    margin-right: 5px;
    font-size: 14px;
  }
} */

.btn-rounded {
  border-radius: 50px;
}

.btn-action {
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.file-upload-wrap {
  .file-upload-btn {
    width: 112px;
    height: 26px;
    border-radius: 3px;
    background-color: #fff;
    border: 1px solid #f7931e;
    font-size: 11px;
    color: #f7931e;
    box-shadow: none;
    text-transform: capitalize;
    font-weight: normal;

    &:hover {
      width: 112px;
      height: 26px;
      border-radius: 3px;
      background-color: #fff;
      border: 1px solid #f7931e;
      font-size: 11px;
      color: #f7931e;
      box-shadow: none;
      text-transform: capitalize;
      font-weight: normal;
    }
  }
}

.table-btn {
  .btn-detail {
    font-size: 13px;
    line-height: 1.15;
    border-radius: 18px;
    border: solid 1px #ffffff;
  }
}

.btn-hide {
  display: none;
}

@media screen and (max-width: 767px) {
  .mobile-create-btn {
    position: fixed;
    z-index: 9;
    bottom: 20px;
    right: 20px;
    padding: 0;
    margin: 0;
    height: auto !important;
  }

  .btn.view-pod-btn svg {
    margin-right: 0px;
  }

  .btn {
    padding: 8px 12px;

    .MuiSvgIcon-root {
      font-size: 22px;
    }
  }
}

.btn-width-100 {
  min-width: 110px;
}

.btn-width-50 {
  min-width: 60px;
}
