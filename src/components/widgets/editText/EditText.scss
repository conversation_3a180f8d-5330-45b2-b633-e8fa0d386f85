.inputWrap{
    label{
        font-size: 12px;
        color: #143751;
        margin-bottom: 4px;
        .mandatoryFlied{
            color: red;
            margin-left: 4px;
        }
    }
   
    .MuiInputBase-input{
        color: #143751;
        font-size: 15px;
        padding: 0 14px;
        height: 44px;
        border-radius: 3px;
        border: solid 2px #E0E4E6;
        box-sizing: border-box;
        &::before,
        &::after {
            display: none;
        }
    }
    .standard{
        label{
            margin-bottom: 0;
            line-height: normal;
        }
        .MuiInputBase-input{
            border: none;
            padding: 0;
            height: 32px;
        }
        .MuiInputBase-root:hover::before{
            border-bottom: 1px solid rgba(0, 0, 0, 0.42);
        }
        .MuiInputBase-root::after{
            border-bottom: none;
        }
    }
    .MuiOutlinedInput-notchedOutline{
        border: none;
    }
    .INR{
        position: absolute;
        left: 6px;
        top: 0;
        bottom: 0;
        margin: auto;
        display: inline-table;
        font-size: 13px;
        color: #797979;
    }
    /* Label Checkbox */
    .label_Checkbox {
        .MuiFormControlLabel-root{
            margin: 0;
            .MuiButtonBase-root{
                margin: 0;
                padding: 0;
                .MuiSvgIcon-root{
                    width: 18px;
                    height: 18px;
                    padding: 0;
                    margin-right: 4px;
                    color: #707070;
                }
            }
            .Mui-checked{
                .MuiSvgIcon-root{
                    color: #ff9119;
                }
            }
            .MuiTypography-root{
                font-size: 12px;
                line-height: normal;
            }
            .Mui-disabled{
                .MuiSvgIcon-root{
                    color: inherit;
                }
            }
        }  
    }  
}
.inputWrap:has(.standard),
.date-time-wrap:has(.standard) {
    label{
        line-height: normal;
        margin: 0;
    }
    .MuiInputBase-input{
        height: 32px;
    }
  }
.inputDisabled{
    background-color: #f9f9f9;
    padding: 4px 0px 0 4px;
    label{
        opacity: 0.7;
    }
    .Mui-disabled{
        // opacity: 0.8 !important;
        -webkit-text-fill-color: inherit !important;
    }
}  
.height40{
    .MuiInputBase-input{
        height: 40px;
    }
}

.row-multi-input{
    label{
        font-size: 12px;
        color: #143751;
        font-weight: 500;
        margin-bottom: 4px;
        .mandatoryFlied{
            color: red;
            margin-left: 4px;
        }
    }
}


