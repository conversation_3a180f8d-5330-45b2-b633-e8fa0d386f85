import { TextField, Box } from '@mui/material'
import { useEffect, useRef } from 'react'
import './EditText.scss'

interface EditTextProps {
  label?: string
  placeholder: string
  value: string | number | undefined
  onChange: (value: any) => void
  validator?: RegExp | undefined
  maxLength: number
  type?: string
  name?: string
  error?: any
  required?: boolean
  toolTip?: () => React.ReactNode
  disabled?: boolean
  endAdornment?: any
  autoCaps?: boolean
  mandatory?: boolean
  variant?: 'filled' | 'outlined' | 'standard'
  className?: string
  styleName?: string
  labelCheckBox?: any
}

const EditText = (props: EditTextProps) => {
  const {
    label,
    placeholder,
    value,
    onChange,
    maxLength,
    type = 'text',
    name,
    required = false,
    error,
    toolTip,
    disabled,
    endAdornment,
    autoCaps,
    mandatory,
    variant = 'outlined',
    className = '',
    styleName = '',
    labelCheckBox,
  } = props

  const inputRef = useRef<any>(null)

  useEffect(() => {
    if (error && inputRef.current) {
      inputRef.current.focus()
    }
  }, [error])

  return (
    <Box className={`inputWrap ${styleName}`}>
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        mb={0}
      >
        <label className="d-flex align-items-center">
          <span>
            {label}
            {mandatory && <span className="mandatoryFlied">*</span>}
          </span>
          {toolTip && <span>{toolTip()}</span>}
          {labelCheckBox && (
            <span className="label_Checkbox">{labelCheckBox}</span>
          )}
        </label>
      </Box>

      <TextField
        inputRef={inputRef}
        name={name}
        variant={variant}
        className={`${className} ${variant}`}
        fullWidth
        placeholder={placeholder}
        value={value ?? ''}
        required={required}
        type={type}
        disabled={disabled}
        helperText={error || ''}
        error={Boolean(error)}
        InputProps={{
          endAdornment: endAdornment,
        }}
        inputProps={{
          maxLength: maxLength,
          autoComplete: 'new-password',
        }}
        onChange={(event) => {
          const val = event.target.value
          if (val.length <= maxLength) {
            onChange(autoCaps ? val.toUpperCase() : val)
          }
        }}
      />
    </Box>
  )
}

export default EditText
