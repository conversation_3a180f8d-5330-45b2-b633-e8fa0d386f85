.card-wrap {
  padding: 0 15px;
  
  .card-list {
    border-radius: 10px;
    box-shadow: 0 2px 4px #00000029;
    background-color: #fff;
    margin-bottom: 10px;
    position: relative;
    padding: 10px 0 0;
    
    .trip-li {
      padding: 0 8px;
      margin-bottom: 8px;
    }
    
    .col-auto,
    .col {
      padding: 0 5px;
      line-height: normal;
    }
    
    .small-title {
      font-size: 10px;
      line-height: 15px;
      font-weight: normal;
      color: #768a9e;
    }
    
    .title,
    .overflow-tooltip {
      font-size: 12px;
      margin: 0;
      font-weight: 400;
      color: #083654;
    }
    
    .btn {
      font-size: 13px;
      padding: 4px 10px;
      margin-bottom: 7px;
      height: auto;
      
      .MuiSvgIcon-root {
        font-size: 14px;
      }
      
      img {
        margin-right: 10px;
      }
    }
    
    .lane-item {
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 180px;
      
      svg {
        font-size: 13px;
        margin: 0 3px;
      }
    }
  }
}

@media (max-width: 767px) {
  .card-list {
    .btn {
      margin-right: 15px;
      
      &.btn-mobile-ml {
        margin-left: 14px;
      }
    }
    
    .modifyb {
      margin-left: 0;
    }
    
    .view-mob {
      min-width: auto;
      margin: 4px 0;
      padding: 4px 10px;
    }
  }
  
  .table-list-mobile {
    .table-list-view {
      .MuiTable-root {
        border-spacing: 0 0;
        background-color: #f9f9f9;
      }
      
      .MuiTableCell-body {
        &:first-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
        
        height: 30px;
        font-size: 10px;
        line-height: 1.1;
        color: #083654;
      }
      
      .table-wrapper {
        margin-bottom: -2px;
        min-height: 60px;
        max-height: 219px;
      }
      
      .MuiTableRow-root {
        .MuiTableCell-root {
          padding: 0 10px;
        }
        
        .MuiTableCell-head {
          padding: 7px 10px 10px;
          font-size: 9px;
        }
      }
    }
  }
  
  .collapse-icon {
    > span {
      display: flex;
      font-size: 12px;
      line-height: 1.17;
      align-items: center;
      color: #006cc9;
    }
    
    .MuiSvgIcon-root {
      margin-right: 10px;
      width: 20px;
      height: 20px;
      border: solid 1px #006cc9;
      background-color: #f9f9f9;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      font-size: 16px;
      color: #006cc9;
    }
  }
}