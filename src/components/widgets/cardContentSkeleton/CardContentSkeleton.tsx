import { Skeleton } from '@mui/material'
import './CardContentSkeleton.scss'
import CardComponent from '../../cardComponent/CardComponent'

interface CardContentSkeletonProps {
  row: number
  column: number
  className?: any
  cardClassName?: any
}

export default function CardContentSkeleton(props: CardContentSkeletonProps) {
  const { row, column, className, cardClassName } = props

  return (
    <CardComponent styleName={cardClassName}>
      <div
        className={
          className
            ? 'card-content-skeleton ' + className
            : 'card-content-skeleton'
        }
      >
        <div className="container-fluid">
          {Array.from(Array(row)).map((_element: any, index: any) => (
            <div className="row" key={index}>
              {Array.from(Array(column)).map((_element: any, index: any) => (
                <div className={'col-' + 12 / column} key={index}>
                  <Skeleton animation="wave" className="skel-label" />
                  <div className="skeleton-bg">
                    <Skeleton animation="wave" />
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </CardComponent>
  )
}
