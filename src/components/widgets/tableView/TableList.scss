/* lane item */
/* table-detail-listing */
/* table field */
.table-list-view {
  .table {
    th {
      vertical-align: middle;
    }

    td {
      vertical-align: middle;
    }
  }

  .MuiPaper-rounded {
    border-radius: 0;
    box-shadow: none;
    background: transparent;
  }

  .MuiTable-root {
    border-collapse: separate;
    border-spacing: 0 5px;
  }

  .MuiTableCell-root {
    border: none;
  }
  .Mui-selected {
    .MuiTableCell-body {
      border-top: solid 1px #ff8900;
      border-bottom: solid 1px #ff8900;
      &:first-child {
        border-left: 1px solid #ff8900;
      }
      &:last-child {
        border-right: 1px solid #ff8900;
      }
    }
  }
  .MuiTableRow-root {
    .MuiTableCell-root {
      padding: 6px 10px;
      min-width: 90px;
      max-width: 320px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .MuiTableCell-head {
      background: #eaeff3;
      border-radius: 0 0 0 0;
      color: #6d7e90;
      font-size: 12px;
      font-weight: 500;
      height: 42px;
      line-height: 1.17;
      padding: 0 10px;
    }
  }

  .MuiTableCell-body {
    font-size: 14px;
    line-height: 1.43;
    color: #083654;
    background: #fff;
    border-bottom: 1px solid #ebeff3;
    border-top: 1px solid #ebeff3;
    height: 54px;

    &:first-child {
      border-left: 1px solid #ebeff3;
    }

    &:last-child {
      border-right: 1px solid #ebeff3;
    }

    .btn {
      height: auto;
      font-size: 12px;
    }
  }

  .btn {
    span {
      + {
        .MuiSvgIcon-root {
          font-size: 15px;
        }
      }
    }
  }

  .table-wrapper {
    min-height: 500px;
    max-height: 66vh;
    overflow: auto;

    ///////
    &::-webkit-scrollbar {
      width: 7px;
      height: 7px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      -webkit-border-radius: 10px;
      border-radius: 10px;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      -webkit-border-radius: 10px;
      border-radius: 10px;
      background-color: grey;
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
    }
  }

  .MuiTablePagination-toolbar {
    border-radius: 4px;
    border: solid 1px #ebeff3;
    background-color: #fff;
    margin-top: 15px;
    color: #808283;
    font-size: 14px;
  }
  .MuiTablePagination-selectLabel {
    margin: 0;
  }
  .MuiTablePagination-displayedRows {
    margin: 0;
    margin-right: 30px;
  }
}
.MuiTablePagination-actions {
  margin-left: 0 !important;
}

// only table list
.table-detail-listing {
  .table-list-view {
    .table-wrapper {
      min-height: inherit;
      .MuiTable-root {
        border-collapse: initial;
        border-spacing: 0px;
        .MuiTableCell-head {
          font-size: 12px;
          color: #143751;
          opacity: 0.62;
          font-weight: 400;
          border-radius: 0px;
          background: #f5f5f5 0% 0% no-repeat padding-box;
          &:first-child {
            border-radius: 6px 0px 0px 0px;
          }
          &:last-child {
            border-radius: 0px 6px 0px 0px;
          }
        }
        .MuiTableCell-head,
        .MuiTableCell-body {
          padding: 6px 10px;
          height: 40px;
        }
        .MuiTableCell-body {
          font-size: 14px;
          color: #143751;
          border-top: none;
        }
      }
      .footer_wrap {
        .MuiTableCell-root {
          padding: 0;
          .cell {
            margin-top: 8px;
            padding: 10px 10px;
            color: #143751;
            background-color: rgba(245, 245, 245, 0.75);
            border: solid 1px rgba(245, 245, 245, 0.95);
            min-height: 43px;
            .title {
              font-size: 14px;
              margin: 0;
              font-weight: 500;
              display: flex;
              align-items: center;
              .divider {
                margin: 0 14px;
                height: 20px;
                border-right: solid 2px rgb(219 219 219);
              }
            }
          }
        }
        .MuiTableCell-footer:first-child .cell {
          border-radius: 6px 0px 0px 6px;
          border-right: none;
        }
        .MuiTableCell-footer:last-child .cell {
          border-radius: 0px 6px 6px 0px;
          border-left: none;
        }
      }
    }
  }
}
