import { TableCell } from '@mui/material'
import React from 'react'
import Button from '../../button/Button'
import { OverflowTip } from '../../tooltip/OverFlowToolTip'
import './TableCollapseList.scss'

interface TableColumnsProps {
  tableColumns?: Array<any>
  data: any
  onClickActionButton?: (row: any) => void
  actionButtonTitle?: string
  actionButtonStyle?: string
  totalCount?: number
  icon?: any
  onClickWayPoints?: (event: React.MouseEvent<HTMLButtonElement> | null) => void
  noDataView?: any
}

const TableDataColumns = (props: TableColumnsProps) => (
  <>
    {props.tableColumns &&
      props.tableColumns.map((column: any, index: number) => {
        if (column.type === 'children') {
          return null
        }
        return (
          (column.type && column.type === 'action' && (
            <TableCell key={index} align="left">
              <Button
                buttonStyle={
                  column.class ? column.class() : props.actionButtonStyle
                }
                title={
                  column.buttonLabel
                    ? column.buttonLabel
                    : props.actionButtonTitle
                }
                rightIcon={column.icon && column.icon}
                leftIcon={column.leftIcon && column.leftIcon}
                onClick={(_event: any) =>
                  (column.onClickActionButton &&
                    column.onClickActionButton(props.data)) ||
                  (props.onClickActionButton &&
                    props.onClickActionButton(props.data))
                }
              />
            </TableCell>
          )) ||
          (column.customView && (
            <TableCell
              key={index}
              align="left"
              className={
                column.class ? column.class(props.data[column.id]) : ''
              }
            >
              {column.customView(props.data)}
            </TableCell>
          )) || (
            <TableCell
              key={index}
              align="left"
              className={
                column.class ? column.class(props.data[column.id]) : ''
              }
            >
              <OverflowTip
                text={
                  column.format
                    ? column.format(props.data[column.id])
                    : props.data[column.id]
                }
              />
            </TableCell>
          )
        )
      })}
  </>
)

export default TableDataColumns
