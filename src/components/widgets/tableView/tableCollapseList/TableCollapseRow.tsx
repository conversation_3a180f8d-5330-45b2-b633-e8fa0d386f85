import React from 'react'
import {
  Icon<PERSON><PERSON>on,
  <PERSON>lapse,
  TableCell,
  TableRow,
  useTheme,
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { dataNotFoundMessage } from '@/constant/MessageUtils'
import TableCollapseDetail from './TableCollapseDetail'
import './TableCollapseList.scss'
import TableDataColumns from './TableDataColumns'

interface TableRowProps {
  tableColumns: Array<any>
  childrenColumns?: Array<any>
  data: any
  onClickActionButton?: (row: any) => void
  actionButtonTitle?: string
  actionButtonStyle?: string
  totalCount?: number
  icon?: any
  onClickWayPoints?: (event: React.MouseEvent<HTMLButtonElement> | null) => void
  noDataView?: any
  childElementKey?: any
  expand: boolean
  changeExpand: any
  childColSpan: number
  emptyChildMessage?: string
}

function TableCollapseRow({
  expand,
  changeExpand,
  data,
  childrenColumns,
  childElementKey,
  emptyChildMessage,
  childColSpan,
  ...props
}: TableRowProps) {
  const theme = useTheme()

  return (
    <>
      <TableRow>
        <TableCell className="expand-btn active">
          <IconButton
            onClick={changeExpand}
            aria-expanded={expand}
            aria-label="show more"
            sx={{
              transform: expand ? 'rotate(180deg)' : 'rotate(0deg)',
              marginLeft: 'auto',
              transition: theme.transitions.create('transform', {
                duration: theme.transitions.duration.shortest,
              }),
            }}
          >
            <ExpandMoreIcon />
          </IconButton>
        </TableCell>
        <TableDataColumns data={data} {...props} />
      </TableRow>

      {expand && (
        <>
          {data && data[childElementKey] ? (
            <TableRow>
              <TableCell colSpan={childColSpan} className="collapse-cell">
                <Collapse in={expand} timeout="auto" unmountOnExit>
                  <TableCollapseDetail
                    tableColumns={childrenColumns}
                    data={data[childElementKey]}
                  />
                </Collapse>
              </TableCell>
            </TableRow>
          ) : (
            <TableRow>
              <TableCell className="shipment-not-found" colSpan={childColSpan}>
                {emptyChildMessage || dataNotFoundMessage}
              </TableCell>
            </TableRow>
          )}
        </>
      )}
    </>
  )
}

export default TableCollapseRow
