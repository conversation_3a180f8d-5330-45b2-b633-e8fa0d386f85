import { Tooltip, tooltipClasses, styled, TooltipProps } from '@mui/material'

export const SubmitTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#333 !important',
    color: '#fff',
    fontSize: '12px',
    borderRadius: 6,
    textAlign: 'center',
    fontWeight: 400,
    maxWidth: 200,
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: '#333',
  },
}))
