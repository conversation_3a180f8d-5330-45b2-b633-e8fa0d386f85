import React from 'react'
import { Tooltip, tooltipClasses, TooltipProps } from '@mui/material'
import { styled } from '@mui/material/styles'

const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
  // eslint-disable-next-line no-empty-pattern
))(({}) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    color: 'rgba(255, 255, 255, 0.99)',
    fontSize: 12,
  },
}))

interface CustomToolTipProps {
  title: string
  placement?: TooltipProps['placement']
  children: React.ReactElement
}

export function CustomToolTip({
  title,
  placement = 'top-start',
  children,
}: CustomToolTipProps) {
  return (
    <LightTooltip title={title} disableTouchListener placement={placement}>
      {children}
    </LightTooltip>
  )
}
