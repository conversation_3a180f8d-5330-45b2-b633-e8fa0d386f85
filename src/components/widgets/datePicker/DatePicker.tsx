import React, { useEffect, useRef } from 'react'
import Styles from './DatePicker.module.scss'
import {
  ClearIcon,
  LocalizationProvider,
  MobileDatePicker,
  MobileDateTimePicker,
} from '@mui/x-date-pickers'
import {
  displayDateFormatter,
  displayDateTimeFormatter,
} from '@/utils/DateUtils'
import { IconButton, InputAdornment } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs, { Dayjs } from 'dayjs'

interface DatePickerProps {
  label?: string
  styleName?: string
  mandatory?: boolean
  icon?: React.ReactNode
  iconPosition?: 'start' | 'end'
  dateAdapter?: any
  value: Dayjs | null | undefined
  onChange: (date: Dayjs | null) => void
  placeholder: string
  disabled?: boolean
  error?: string
  disableFuture?: boolean
  disablePast?: boolean
  minDate?: Dayjs
  maxDate?: Dayjs
  includeTime?: boolean
  inputStyle?: string
  minDateTime?: Dayjs
  maxDateTime?: Dayjs
  clearable?: boolean
}

export default function DatePicker({
  label,
  styleName,
  mandatory,
  icon,
  iconPosition = 'end',
  dateAdapter = AdapterDayjs,
  value,
  placeholder,
  disabled,
  onChange,
  error,
  disableFuture = false,
  disablePast = false,
  minDate,
  maxDate,
  includeTime = false,
  inputStyle,
  minDateTime,
  maxDateTime,
  clearable = false,
}: DatePickerProps) {
  // preserve last valid selection
  const lastValidDate = useRef<Dayjs | null>(value ?? null)

  useEffect(() => {
    lastValidDate.current = value ?? null
  }, [value])

  const clearButton =
    clearable && value ? (
      <IconButton
        size="small"
        onClick={(e) => {
          e.stopPropagation()
          onChange(null)
        }}
        edge={iconPosition}
        aria-label="clear"
      >
        <ClearIcon fontSize="small" />
      </IconButton>
    ) : null

  const Adornment = icon ? (
    <InputAdornment position={iconPosition}>
      {iconPosition === 'start' ? (
        <>
          <IconButton edge="start">{icon}</IconButton>
          {clearButton}
        </>
      ) : (
        <>
          {clearButton}
          <IconButton edge="end">{icon}</IconButton>
        </>
      )}
    </InputAdornment>
  ) : null

  // helper to pick a valid Dayjs or null
  const normalize = (d: Dayjs | null | undefined): Dayjs | null => {
    if (!d || !dayjs(d).isValid()) return null
    return dayjs(d)
  }

  return (
    <div className={`dateTimePicker ${Styles.datePicker} ${styleName || ''}`}>
      {label && (
        <label htmlFor={label}>
          {label}
          {mandatory && <span className={Styles.mandatoryFlied}>*</span>}
        </label>
      )}
      <div className={`${Styles.datePickerInput} ${inputStyle || ''}`}>
        <LocalizationProvider dateAdapter={dateAdapter}>
          {includeTime ? (
            <MobileDateTimePicker
              format={displayDateTimeFormatter}
              value={normalize(value)}
              onAccept={(d) => {
                const date = normalize(d)
                if (!date) {
                  return onChange(null)
                }
                const now = dayjs()
                if (disableFuture && date.isAfter(now)) {
                  return onChange(lastValidDate.current)
                }
                if (disablePast && date.isBefore(now)) {
                  return onChange(lastValidDate.current)
                }
                onChange(date)
              }}
              minDateTime={minDateTime}
              maxDateTime={maxDateTime}
              disableFuture={disableFuture}
              disablePast={disablePast}
              disabled={disabled}
              slotProps={{
                toolbar: {
                  toolbarFormat: 'MMM DD',
                  hidden: false,
                  toolbarPlaceholder: null,
                },
                textField: {
                  placeholder,
                  helperText: error,
                  InputProps: {
                    [iconPosition === 'start'
                      ? 'startAdornment'
                      : 'endAdornment']: Adornment,
                  },
                  value: normalize(value),
                },
              }}
              localeText={{ toolbarTitle: '' }}
              disableHighlightToday
              ampm
            />
          ) : (
            <MobileDatePicker
              format={displayDateFormatter}
              value={normalize(value)}
              onAccept={(d) => {
                const date = normalize(d)
                onChange(date)
              }}
              minDate={minDate}
              maxDate={maxDate}
              disableFuture={disableFuture}
              disablePast={disablePast}
              disabled={disabled}
              slotProps={{
                toolbar: {
                  toolbarFormat: 'ddd, MMM DD',
                  hidden: false,
                  toolbarPlaceholder: null,
                },
                textField: {
                  placeholder,
                  helperText: error,
                  InputProps: {
                    [iconPosition === 'start'
                      ? 'startAdornment'
                      : 'endAdornment']: Adornment,
                  },
                  value: normalize(value),
                },
              }}
              localeText={{ toolbarTitle: '' }}
              disableHighlightToday
            />
          )}
        </LocalizationProvider>
      </div>
    </div>
  )
}
