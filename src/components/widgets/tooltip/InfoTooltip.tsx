import React from 'react'
import { Tooltip, tooltipClasses, styled, TooltipProps } from '@mui/material'
import { Info } from '@mui/icons-material'
import './InfoTooltip.scss'
import { isMobile } from '@/utils/ViewUtils'

interface InfoTooltipProps {
  title: any
  infoText?: any
  valueClassName?: string
  customIcon?: any
  className?: string
  style?: any
  wrap?: any
  placement?: TooltipProps['placement']
  arrow?: boolean
  children?: React.ReactNode
  disableInMobile?: boolean | string
  open?: boolean
  iconClassName?: string
}

InfoTooltip.defaultProps = {
  placement: 'bottom-start',
  disableInMobile: false,
}

// Styled Tooltip using MUI v6 `styled` API
const CustomTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#fff',
    color: '#111212',
    fontSize: '14px',
    minWidth: '64px',
    maxWidth: isMobile ? '320px' : '400px',
    boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.16)',
    margin: '6px 0px',
    borderRadius: 4,
    textAlign: 'center',
    padding: '7px 9px',
    fontWeight: 'normal',
  },
}))

export function InfoTooltip(props: InfoTooltipProps) {
  const {
    placement,
    arrow,
    title,
    children,
    disableInMobile,
    className,
    infoText,
    customIcon,
    valueClassName,
    open,
    iconClassName,
  } = props

  return (
    <CustomTooltip
      open={open}
      placement={placement}
      arrow={arrow}
      disableHoverListener={
        disableInMobile === 'false' || disableInMobile === false
          ? false
          : isMobile
      }
      enterTouchDelay={0}
      leaveTouchDelay={15000}
      disableTouchListener={false}
      title={
        <div className={`info-tooltip ${className || ''}`}>
          {title}
          {children}
        </div>
      }
    >
      {infoText ? (
        <div
          className={
            valueClassName
              ? `tooltip-info text-truncate ${valueClassName}`
              : 'tooltip-info text-truncate'
          }
        >
          {infoText || 'NA'}
        </div>
      ) : (
        <span className={`tool-tip-icon ${iconClassName || ''}`}>
          {customIcon || <Info className="blue-text icon-info" />}
        </span>
      )}
    </CustomTooltip>
  )
}
