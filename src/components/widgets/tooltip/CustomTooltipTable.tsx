import { Tooltip, tooltipClasses } from '@mui/material'
import { Info } from '@mui/icons-material'
import { isMobile } from '@/utils/ViewUtils'
import './CustomToolTipTable.scss'

interface CustomTooltipTableProps {
  tableColumn: Array<any> | undefined
  tableData: Array<any> | undefined
  infoText?: string
  valueClassName?: string
  showStringValue?: boolean
  customIcon?: any
  className?: string
  style?: object
  wrap?: any
  placement?: any
  arrow?: any
  displayZone?: any
  zoneDetails?: any
  disableInMobile?: boolean
}

export const CustomTooltipTable = (props: CustomTooltipTableProps) => {
  const {
    tableData,
    tableColumn,
    infoText,
    valueClassName,
    showStringValue,
    customIcon,
    className,
    wrap,
    placement,
    arrow = true,
    disableInMobile = false,
    style = {},
  } = props

  const tooltipStyles = {
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: '#fff',
      color: '#111212',
      fontSize: '12px',
      minWidth: '64px',
      boxShadow: '0 0 4px rgba(0, 0, 0, 0.16)',
      margin: '6px 0px',
      borderRadius: '8px',
      padding: '0px',
      fontWeight: 'normal',
      overflow: 'hidden',
      ...style,
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: '#fff',
    },
  }

  return (
    <div className={wrap ? '' : 'tooltip-table-wrap'}>
      <Tooltip
        title={
          <div className={`tooltip-table-info ${className}`}>
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    {tableColumn?.map((element, index) => (
                      <th key={index}>{element.description}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {tableData?.map((element, index) => (
                    <tr key={index}>
                      {tableColumn?.map((column, colIndex) => {
                        if (column.customView) return column.customView(element)

                        if (column.format) {
                          return (
                            <td key={colIndex}>
                              {column.format(element[column.name])}
                            </td>
                          )
                        }

                        return (
                          <td key={colIndex}>
                            {setInputValueFromElement(element[column.name])}
                          </td>
                        )
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        }
        placement={placement || 'top'}
        disableHoverListener={disableInMobile ? isMobile : false}
        leaveTouchDelay={15000}
        enterTouchDelay={0}
        arrow={arrow}
        sx={tooltipStyles}
      >
        {infoText ? (
          <div className={`media-body ${valueClassName || ''}`}>{infoText}</div>
        ) : (
          <span className="tooltip-icon">
            {customIcon || <Info className="text-orange icon-info" />}
          </span>
        )}
      </Tooltip>
    </div>
  )

  function setInputValueFromElement(valueElement: any) {
    if (showStringValue) {
      try {
        return valueElement
      } catch {
        return ''
      }
    } else {
      try {
        return isNaN(Number.parseFloat(valueElement))
          ? valueElement
          : Number.parseFloat(valueElement).toFixed(2)
      } catch {
        return ''
      }
    }
  }
}
