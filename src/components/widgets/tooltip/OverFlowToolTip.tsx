import { useEffect, useRef, useState } from 'react'
import { Tooltip, styled, tooltipClasses, TooltipProps } from '@mui/material'
import './InfoTooltip.scss'
import { isMobile } from '@/utils/ViewUtils'

interface OverFlowTooltipProps {
  text: any
  cellIcon?: any
  style?: object
}

// Custom styled tooltip using MUI v6 `styled` API
const CustomTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    // background: '#fff',
    color: '#133751',
    fontSize: '10px',
    minWidth: '64px',
    maxWidth: isMobile ? 320 : 400,
    boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.16)',
    // margin: '6px 0px',
    // padding: '7px 9px',
    borderRadius: 4,
    textAlign: 'center',
    fontWeight: 'normal',
  },
}))

export function OverflowTip(props: OverFlowTooltipProps) {
  const textElementRef = useRef<HTMLDivElement>(null)
  const [hoverStatus, setHover] = useState(false)

  const compareSize = () => {
    const el = textElementRef.current
    if (!el) return
    const isOverflowing = el.scrollWidth > el.clientWidth
    setHover(isOverflowing)
  }

  useEffect(() => {
    compareSize()
    window.addEventListener('resize', compareSize)
    return () => {
      window.removeEventListener('resize', compareSize)
    }
  }, [])

  return (
    <CustomTooltip
      title={<div className="info-tooltip">{props.text || 'NA'}</div>}
      disableHoverListener={!hoverStatus}
      leaveTouchDelay={15000}
      disableTouchListener={false}
      enterTouchDelay={0}
    >
      <div
        className="overflow-tooltip"
        ref={textElementRef}
        style={{
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: '230px',
          ...(props.style || {}),
        }}
      >
        {props.cellIcon}
        {props.text || 'NA'}
      </div>
    </CustomTooltip>
  )
}
