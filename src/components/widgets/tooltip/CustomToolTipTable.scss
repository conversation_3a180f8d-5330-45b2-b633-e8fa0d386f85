// Variables
$primary-gradient-start: #f7931e;
$primary-gradient-end: #f73f1e;
$border-color: #f79d7f;
$text-color: #083654;
$bg-color: #f9f9f9;
$white: #fff;
$border-radius: 4px;
$box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16);

/* Table tooltip */
.tooltip-table-info {
  width: 100%;
  padding: 0;
  margin: 0;
  box-shadow: $box-shadow;
  
  .table {
    margin: 0;
    padding: 0;
    width: 100%;
    
    th {
      background-image: linear-gradient(to bottom, $primary-gradient-start, $primary-gradient-end);
      border-right: solid 1px $border-color;
      color: $white;
      
      &:first-child {
        border-top-left-radius: $border-radius;
      }
      
      &:last-child {
        border-top-right-radius: $border-radius;
        border-right: none;
      }
    }
    
    td {
      color: $text-color;
      border: solid 1px $bg-color;
    }
    
    th,
    td {
      font-weight: 500;
      padding: 6px 12px;
      letter-spacing: 0.6px;
      font-size: 11px;
    }
  }
  
  .table-responsive {
    pointer-events: all;
  }
  
  tbody tr {
    word-break: break-all;
  }
}

.tooltip-icon {
  svg {
    color: $primary-gradient-start;
    font-size: 17px;
    margin-left: 2px;
  }
}

// .tooltip-table-info .table thead tr {
//   background-image: linear-gradient(to bottom, $primary-gradient-start, $primary-gradient-end);
// }