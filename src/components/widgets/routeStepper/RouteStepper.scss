.lane-stepper-wrap {
  margin-left: -10px;
  margin-right: -10px;
  
  .MuiStepConnector-vertical,
  .MuiStepContent-root {
    margin-left: 7px;
  }
  
  .MuiStepConnector-active {
    .MuiStepConnector-lineVertical {
      border-left: 2px solid #006cc9;
    }
  }
  
  .MuiStepLabel-iconContainer {
    padding-right: 15px;
  }
  
  .item-title {
    font-size: 13px;
    line-height: normal;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
    color: #323232;
    font-weight: normal;
  }
  
  .MuiStep-completed {
    .MuiStepLabel-root {
      .delayed {
        color: rgba(0, 0, 0, 1);
      }
    }
  }
  
  /* progress animation */
  .MuiStepper-vertical {
    position: relative;
    overflow: hidden;
    padding: 20px 0 0 10px;
    background: transparent;
  }
  
  .MuiStepConnector-root {
    display: none;
  }
  
  .MuiStepLabel-root {
    align-items: flex-start;
    padding-bottom: 0;
    padding-top: 0;
  }
  
  .MuiStep-vertical {
    /* min-height: 50px; */
    max-height: 150px;
  }
  
  .MuiStep-root {
    padding-bottom: 30px;
    position: relative;
    overflow: hidden;
    padding: 0px 14px 0;
    
    &:first-child {
      padding-top: 0;
    }
  }
  
  .Mui-completed {
    &::before {
      content: "";
      position: absolute;
      width: 2px;
      height: 100%;
      background: #006cc9;
      left: 19px;
      padding: 0;
    }
  }
  
  .MuiStep-completed {
    position: relative;
    /* overflow: hidden; */
    
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 100%;
      background: green;
      left: 19px;
      padding: 0;
    }
  }
  
  .MuiStepLabel-labelContainer {
    margin-top: -8px;
    
    .MuiStepLabel-label {
      color: #083654;
      font-weight: 400;
      justify-content: space-between;
      font-size: 20px;
      line-height: 1.2;
      width: 100%;
      display: flex;
      align-items: center;
    }
  }
}

.label {
  font-size: 10px;
  line-height: 1.15;
  margin-bottom: 5px;
  color: #768A9E;
}