import { Step, StepLabel, Stepper, styled, StepIconProps } from '@mui/material'
import {
  originLabel,
  destinationLabel,
  wayPointsLabel,
} from '@/constant/MessageUtils'
import './RouteStepper.scss'
import { CustomToolTip } from '../CustomToolTip'

const CompletedIcon = styled('div')({
  backgroundColor: '#006CC9',
  zIndex: 1,
  color: '#ffffff',
  width: 14,
  height: 14,
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
})

function CustomStepIcon(props: StepIconProps) {
  const { completed } = props
  return completed ? <CompletedIcon /> : <div />
}

interface RouteStepperProps {
  steps: Array<{ address?: string }>
}

export default function RouteStepper({ steps }: RouteStepperProps) {
  const drawStepper = (element: any, index: number) => {
    return (
      <Step key={index}>
        <StepLabel slots={{ stepIcon: CustomStepIcon }} sx={{ height: 50 }}>
          <CustomToolTip title="">
            <div>
              <label className="label">
                {index === 0
                  ? originLabel
                  : index === steps.length - 1
                    ? destinationLabel
                    : `${wayPointsLabel} ${index}`}
              </label>
              <div className="d-flex align-items-center">
                <img
                  className="me-2"
                  style={{ height: 20, width: 20 }}
                  src="/images/Address.svg"
                  alt="lane"
                />
                {element?.address && (
                  <div className="item-title text-truncate">
                    {element.address}
                  </div>
                )}
              </div>
            </div>
          </CustomToolTip>
        </StepLabel>
      </Step>
    )
  }

  return (
    <div className="lane-stepper-wrap">
      <Stepper activeStep={steps.length - 1} orientation="vertical">
        {steps.map((element, index) => drawStepper(element, index))}
      </Stepper>
    </div>
  )
}
