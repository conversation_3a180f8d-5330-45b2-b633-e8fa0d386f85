import './TextArea.scss'
import { TextareaAutosize } from '@mui/material'

interface TextAreaProps {
  label?: string
  placeholder: string
  variant?: string | 'standard'
  className?: string
  styleName?: string
  value: string | number | undefined
  maxLength: number
  mandatory?: boolean
  onChange: (value: any) => void
  maxRows?: number
  minRows?: number
  disabled?: boolean
}

function TextArea(props: TextAreaProps) {
  const {
    mandatory,
    label,
    maxLength,
    maxRows,
    minRows,
    disabled,
    value,
    styleName,
  } = props
  return (
    <div className={`textArea_wrap ${styleName} && ${styleName}`}>
      <label>
        {label && <span>{label}</span>}
        {mandatory && <span className={'mandatoryFlied'}>*</span>}
      </label>
      <div className={`${props.variant || ' '}`}>
        <TextareaAutosize
          maxRows={maxRows}
          minRows={minRows}
          placeholder={props.placeholder}
          className={`${props.className || ' '}`}
          maxLength={maxLength}
          disabled={disabled}
          value={value}
          onChange={(text: any) => props.onChange(text.target.value)}
        />
      </div>
    </div>
  )
}

export default TextArea
