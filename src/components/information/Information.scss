.information {
  width: 100%;
  max-width: 100%;
  min-width: 0;
  overflow: hidden;

  .info-heading {
    font-size: 12px;
    letter-spacing: 0px;
    color: #143751;
    opacity: 0.62;
    display: block;
    margin-bottom: 2px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .content-info {
    align-items: center;
    font-size: var(--font-size);
    letter-spacing: 0px;
    color: #143751;
    opacity: 1;
    display: flex;
    width: 100%;
    min-width: 0;
    overflow: hidden;

    .MuiSvgIcon-root {
      color: #768a9e;
      display: inline-flex;
    }

    .blue-icon {
      .MuiSvgIcon-root {
        margin: 0;
        color: #006cc9;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .media .media-body,
  .lane-text {
    width: fit-content;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }

  .pay-info-icon {
    flex-shrink: 0;
    img {
      margin-right: 10px;
    }
  }
  @media (max-width: 767px) {
    .info-heading {
      font-size: 10px;
      margin-bottom: 2px;
      line-height: normal;
    }
    .content-info {
      font-size: 12px;
    }
  }
}
