import './Information.scss'
import { ReactNode, useRef, useEffect, useState } from 'react'
import Tooltip from '@mui/material/Tooltip'

interface InformationProps {
  title?: ReactNode
  text?: string
  icon?: ReactNode
  leftInfo?: ReactNode
  rightInfo?: ReactNode
  leftInfoStyle?: string
  rightInfoStyle?: string
  image?: ReactNode
  sup?: number
  tooltip?: () => ReactNode
  valueTooltip?: () => ReactNode
  valueClassName?: string
  customView?: ReactNode
  className?: string
  customLabel?: ReactNode
}

const OverflowTooltip = ({
  children,
  text,
  className = '',
}: {
  children: ReactNode
  text: string
  className?: string
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const [isOverflowed, setIsOverflowed] = useState(false)

  useEffect(() => {
    const checkOverflow = () => {
      const el = ref.current
      if (el) {
        // More reliable overflow check
        const hasOverflow = el.scrollWidth > el.offsetWidth
        setIsOverflowed(hasOverflow)
      }
    }
    const timeoutId = setTimeout(checkOverflow, 100)
    const resizeObserver = new ResizeObserver(() => {
      checkOverflow()
    })

    if (ref.current) {
      resizeObserver.observe(ref.current)
    }

    window.addEventListener('resize', checkOverflow)
    const current = ref.current
    return () => {
      clearTimeout(timeoutId)
      if (current) {
        resizeObserver.unobserve(current)
      }
      window.removeEventListener('resize', checkOverflow)
    }
  }, [text])

  return (
    <Tooltip
      title={isOverflowed && text ? text : ''}
      arrow
      placement="top"
      disableHoverListener={!isOverflowed}
      enterTouchDelay={50}
      leaveTouchDelay={1500}
      disableFocusListener={false}
      disableTouchListener={false}
      slotProps={{
        tooltip: {
          sx: {
            background: '#ffffff !important',
            color: '#143751',
            fontSize: '12px',
            padding: '4px 8px !important',
            boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.16)',
            borderRadius: '4px',
            maxWidth: '240px',
            width: 'fit-content',
            fontWeight: 'normal',
            textAlign: 'center',
            margin: 0,
          },
        },
        arrow: {
          sx: {
            color: '#fff',
          },
        },
      }}
    >
      <div
        ref={ref}
        className={`text-truncate ${className}`}
        style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          width: '100%',
        }}
      >
        {children}
      </div>
    </Tooltip>
  )
}

function Information({
  title,
  text = 'NA',
  leftInfo,
  rightInfo,
  leftInfoStyle,
  rightInfoStyle,
  image,
  sup,
  tooltip,
  valueTooltip,
  valueClassName,
  customView,
  className = '',
  customLabel,
}: InformationProps) {
  const valueContent = customView ? (
    customView
  ) : sup ? (
    <OverflowTooltip text={text}>
      {text}
      <sup>{text ? sup : ''}</sup>
    </OverflowTooltip>
  ) : valueTooltip ? (
    <div
      className={`media-body text-truncate ${valueClassName || ''}`}
      style={{ width: '100%' }}
    >
      <span>{valueTooltip()}</span>
    </div>
  ) : (
    <OverflowTooltip
      text={text}
      className={`media-body ${valueClassName || ''}`}
    >
      {text}
    </OverflowTooltip>
  )

  return (
    <div
      className={`information d-flex align-items-center ${className}`}
      style={{
        width: '100%',
        maxWidth: '100%',
        minWidth: 0,
        overflow: 'hidden',
      }}
    >
      {image && <div className="pay-info-icon">{image}</div>}
      <div className="flex-grow-1" style={{ minWidth: 0, overflow: 'hidden' }}>
        {title && (
          <div className="d-flex align-items-center">
            <label className="info-heading">
              {title}
              {tooltip && <span>{tooltip()}</span>}
            </label>
            {customLabel}
          </div>
        )}
        <div className="media content-info">
          {leftInfo && (
            <span className={`me-2 left-info ${leftInfoStyle || ''}`}>
              {leftInfo}
            </span>
          )}
          {image && <div className="pay-info-icon">{image}</div>}
          <div className="media-body">{valueContent}</div>
          {rightInfo && (
            <span className={`ms-2 left-info ${rightInfoStyle || ''}`}>
              {rightInfo}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default Information
