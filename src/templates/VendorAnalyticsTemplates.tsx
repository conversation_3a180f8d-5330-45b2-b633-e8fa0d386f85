import { InfoTooltip } from '@/components/widgets/tooltip/InfoTooltip'
import { OverflowTip } from '@/components/widgets/tooltip/OverFlowToolTip'
import { VendorAnalyticsState } from '@/pages/loads/loadDetails/types/LoadDetailsModels'
import { ColumnStateModel } from '@/types/TableColumns'
import { Mail, Phone } from '@mui/icons-material'
import { Radio } from '@mui/material'

export const getVendorAnalyticsColumns = (
  selectedVendor: VendorAnalyticsState | null,
  handleChange: (vendor: any) => void
) => {
  const columnList: ColumnStateModel[] = [
    {
      id: 'action',
      label: 'Action',
      format: (value: any) => value || '.....',
      class: () => 'td-info-wrap',
      customView: (element: VendorAnalyticsState) => {
        const isSelected = selectedVendor?.brokerCode === element.brokerCode

        return (
          <div
            className={`vendor-info ${isSelected ? 'selected-row' : ''}`}
            onClick={() => handleChange(element)}
            style={{ cursor: 'pointer' }}
          >
            <h6>
              {element.brokerName}&lt;{element.brokerCode}&gt;
            </h6>

            <div className="d-flex align-items-center">
              <ul className="d-flex align-items-center">
                <li>
                  <Phone className="orange-color" />
                </li>
                <li>{element.brokerPhone}</li>
              </ul>
              <ul className="d-flex align-items-center vendor-info-mail">
                <li>
                  <Mail className="orange-color" />
                </li>
                <li>
                  <OverflowTip text={element.brokerEmail} />
                </li>
              </ul>
            </div>
          </div>
        )
      },
    },
    {
      id: 'score',
      label: 'Score',
      format: (value: any) => value || 'NA',
      customView: (element: VendorAnalyticsState) => {
        return (
          <div>
            <span className="orange-color">{element.totalScore}</span>/
            {element.totalScoreFactor}
            <InfoTooltip title={'Score'} />
          </div>
        )
      },
    },
    {
      id: 'origin',
      label: 'Origin',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'destination',
      label: 'Destination',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'freightRate',
      label: 'Min - Max Freight Rate (₹)',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'orderCount',
      label: 'No. Of Trips',
      format: (value: any) => value || 'NA',
    },
    {
      id: '',
      label: '',
      format: (value: any) => value || 'NA',
      customView: (element: any) => {
        return (
          <Radio
            checked={
              selectedVendor?.brokerCode
                ? selectedVendor?.brokerCode === element?.brokerCode
                : false
            }
            onChange={() => handleChange(element)}
            value={selectedVendor?.brokerCode}
            name="radio-buttons"
            className="orange-color"
          />
        )
      },
    },
  ]

  return columnList
}
