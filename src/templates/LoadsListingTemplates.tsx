import {
  loadsFulfillmentByValueLabelMapper,
  loadsListingAPIStatusEnum,
  loadsOrderNumberValueLabelMapper,
  loadsOrderStatusValueLabelMapper,
  loadsStatusValueLabelMapper,
} from '@/constant/ArrayList'
import { ColumnStateModel } from '@/types/TableColumns'
export const getLoadsListingColumns = (selectedTab: number) => {
  const columnList: ColumnStateModel[] = [
    { id: 'id', label: 'Load ID', format: (value: any) => value || 'NA' },
    {
      id: 'customerName',
      label: 'Customer Name',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'originName',
      label: 'Origin',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'destinationName',
      label: 'Destination',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'vehicleType',
      label: 'Vehicle Type',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'vehicleNumber',
      label: 'Vehicle Number',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'loadType',
      label: 'Load Type',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'indentType',
      label: 'Indent Type',
      format: (value: any) => value || 'NA',
    },
    {
      id: 'rate',
      label: 'Buyer Rate',
      format: (value: any) => (value ? `₹${value}` : 'NA'),
      class: () => 'orange-color',
      sticky: true,
    },
  ]

  const tabToStatusMap = {
    0: loadsListingAPIStatusEnum.PENDING,
    1: loadsListingAPIStatusEnum.REJECTED,
    2: loadsListingAPIStatusEnum.APPROVED,
  } as Record<number, loadsListingAPIStatusEnum>

  const selectedStatus = tabToStatusMap[selectedTab]

  switch (selectedStatus) {
    case loadsListingAPIStatusEnum.PENDING:
    case loadsListingAPIStatusEnum.REJECTED:
      break
    case loadsListingAPIStatusEnum.APPROVED:
      columnList.splice(
        4,
        0,
        {
          id: 'fulfillmentBy',
          label: 'Fulfillment By',
          format: (value: any) =>
            loadsFulfillmentByValueLabelMapper[
              value as keyof typeof loadsFulfillmentByValueLabelMapper
            ] || 'NA',
          class: (value: any) => {
            if (!value) return 'black-color'
            return value === loadsFulfillmentByValueLabelMapper.MARKET
              ? 'red-color'
              : 'green-color'
          },
        },
        {
          id: 'orderNumber',
          label: 'Order Number',
          format: (value: any) =>
            loadsOrderNumberValueLabelMapper[
              value as keyof typeof loadsOrderNumberValueLabelMapper
            ] || 'NA',
        },
        {
          id: 'orderStatus',
          label: 'Order Status',
          format: (value: any) =>
            loadsOrderStatusValueLabelMapper[
              value as keyof typeof loadsOrderStatusValueLabelMapper
            ] || 'NA',
        }
      )
      columnList.splice(8, 0, {
        id: 'loadStatus',
        label: 'Load Status',
        format: (value: any) =>
          loadsStatusValueLabelMapper[
            value as keyof typeof loadsStatusValueLabelMapper
          ] || 'NA',
      })
      break
  }
  return columnList
}
