export interface ColumnStateModel {
  id: Array<any> | string
  label: string
  buttonLabel?: string
  minWidth?: number
  align?: 'right'
  type?: any
  format?: (value: any) => any
  class?: string | ((value?: any) => string)
  onClickActionButton?: (event: React.MouseEvent<HTMLElement>) => void
  icon?: any
  leftIcon?: any
  staticValue?: string
  customHead?: any
  customView?: any
  cellIconLeft?: any
  styleCss?: any
  mandatoryHeader?: boolean
  isVisible?: boolean
  sticky?: boolean
}
