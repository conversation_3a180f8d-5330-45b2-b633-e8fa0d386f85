import { Task } from '@redux-saga/types'
import { all, take, fork, cancel, call, put, delay } from 'redux-saga/effects'
import { orchestrationRunningStatusEnum } from './constant/ArrayList'
import { isNullValue } from './utils/StringUtils'
import { pollStop, showSuccessAlert } from './redux/slices/AppSlice'

interface ResponseObject {
  customStatus?: any
  runtimeStatus?: any
  output?: any
  status?: any
}
interface ActionObject {
  type: any
  value: any
}

function* startPolling(action: any) {
  const actionParams = Object.assign({}, action.value)
  const { params } = actionParams
  while (true) {
    const response: ResponseObject = yield call(() =>
      actionParams.asyncFetch(params)
    )
    if (isNullValue(response)) {
      actionParams.stopPollingLoader()
      break
    }
    if (response.runtimeStatus === orchestrationRunningStatusEnum.COMPLETED) {
      actionParams.stopPollingLoader(actionParams?.requestType)
      actionParams.stopPollingData(actionParams?.requestType)
      if (actionParams?.requestType === 'CREATE_INDENT') {
        yield put(showSuccessAlert('Indent created successfully'))
      } else if (actionParams?.requestType === 'MOVE_TO_LOAD_BOARD') {
        yield put(showSuccessAlert('Moving to Load board'))
      }
      yield put(pollStop(params))
    }

    if (response.runtimeStatus === orchestrationRunningStatusEnum.FAILED) {
      actionParams.stopPollingLoader(actionParams?.requestType)
      yield put(showSuccessAlert(response.status))

      yield put(pollStop(params))
    }
    yield delay(1000)
  }
}

function* runMultiplePollingTask() {
  const tempObj: any = {}
  while (true) {
    const action: ActionObject = yield take(['POLL_START', 'POLL_STOP'])
    if (action.type === 'POLL_START') {
      const task: Task = yield fork(startPolling, action)
      console.log('x->', action)
      if (
        action &&
        action.value &&
        action.value.params &&
        action.value.params.orchestrationId
      ) {
        const key = action.value.params.orchestrationId
        tempObj[key] = task
      }
    } else {
      if (action && action.value && action.value.orchestrationId) {
        const response = action && action.value && action.value.orchestrationId
        yield cancel(tempObj[response])
        delete tempObj[response]
      }
    }
  }
}

export default function* rootSaga() {
  yield all([runMultiplePollingTask()])
}
