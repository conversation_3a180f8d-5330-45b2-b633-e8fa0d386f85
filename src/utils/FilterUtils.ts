export const loadListingFilters = {
  loadboard_id: 'loadboard_id',
  customerLabel: 'customer_name',
  customerId: 'customer_id',
  originLabel: 'origin',
  destinationLabel: 'destination',
  load_type: 'load_type',
  indent_type: 'indent_type',
  vehicleTypeLabel: 'vehicle_type',
  vehicleNumberLabel: 'vehicle_number',
} as const
export const handleDeleteChips = (
  element: any,
  removeFiltersQueryParams: any,
  keepRouteState = false
) => {
  if (element === 'createdFromDate' || element === 'createdToDate') {
    const secondKey =
      element === 'createdFromDate' ? 'createdToDate' : 'createdFromDate'
    const extraMobileKey =
      element === 'query' ? ['queryField', 'queryFieldLabel'] : []
    removeFiltersQueryParams(
      [element, secondKey, ...extraMobileKey],
      keepRouteState
    )
  } else if (element === 'customerLabel') {
    removeFiltersQueryParams([element, 'customerId'])
  } else {
    removeFiltersQueryParams([element], keepRouteState)
  }
}
