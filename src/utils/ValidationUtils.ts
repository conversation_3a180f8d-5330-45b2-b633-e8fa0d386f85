import { isObjectEmpty } from './StringUtils'

interface ValidationResult {
  isValid: boolean
  error?: string | { [key: string]: string } | { [key: string]: any }
  showAlert?: boolean
}

export type ValidationRule = { type: string; msg?: string; value?: any }
type ValidatorFunction = (
  value: any,
  config?: ValidationRule,
  formData?: any
) => ValidationResult
export interface SchemaWithValidators {
  schema: { [key: string]: ValidationRule[] }
  validators: { [key: string]: ValidatorFunction }
}

export const validateFormData = (
  formData: { [key: string]: any },
  schemaWithValidators: SchemaWithValidators
) => {
  const { schema, validators } = schemaWithValidators
  const errors: { [key: string]: any } = {}
  const alertErrors: { [key: string]: any } = {}
  let isValid = true

  for (const [field, rules] of Object.entries(schema)) {
    for (const rule of rules) {
      const result = validators[rule.type](formData[field], rule, formData)
      if (!result.isValid) {
        if (result.showAlert && isObjectEmpty(alertErrors)) {
          // isObjectEmpty check to show only one alert error at a time
          alertErrors[field] = rule.msg
        } else {
          errors[field] = result.error ?? rule.msg
        }
        isValid = false
        break
      }
    }
  }
  return { isValid, errors, alertErrors }
}
