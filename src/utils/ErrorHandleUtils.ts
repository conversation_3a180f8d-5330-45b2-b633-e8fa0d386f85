import { hideLoader, showErrorAlert } from '@/redux/slices/AppSlice'
import { AppDispatch } from '@/redux/store'

export function handleApiError(error: any, appDispatch: AppDispatch) {
  appDispatch(hideLoader())
  if (isDisplayError(error)) {
    appDispatch(showErrorAlert(error))
  }
}

function isDisplayError(message: string) {
  switch (message) {
    case 'Nothing to display':
      return false
    case 'Unauthorized':
      return false
    case '403':
      return false
    default:
      return true
  }
}
