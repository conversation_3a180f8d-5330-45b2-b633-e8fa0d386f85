import { useLocation } from 'react-router-dom'
import { DispatchPeriodsEnum } from '../constant/ArrayList'
// import {
//   convertDateToServerFromDate,
//   convertDateToServerToDate,
//   getPastDate,
// } from '@/utils/DateUtils'
import { isNullValue } from './StringUtils'
import { isMobile } from './ViewUtils'

function useQuery() {
  return new URLSearchParams(useLocation().search)
}

function createQueryParams(
  searchParams: any,
  chips: any,
  params: any,
  filterKeySet: any
) {
  // eslint-disable-next-line
  filterKeySet &&
    Object.entries(filterKeySet).map((element) => {
      searchParams.delete(element[0])
      searchParams.delete(element[1])
    })

  if (chips) {
    Object.entries(chips).map((p: any) => {
      searchParams.delete(p[0])
      if (!isNullValue(p[1])) {
        searchParams.append(p[0], p[1])
      }
    })
  }
  if (params) {
    Object.entries(params).map((p: any) => {
      searchParams.delete(p[0])
      if (!isNullValue(p[1])) {
        searchParams.append(p[0], p[1])
      }
    })
  }
  return searchParams
}

function getFilterChipsAndParams(queryParams: any, filterKeys: any) {
  const chips: any = {}
  const params: any = {}
  if (queryParams) {
    Object.keys(filterKeys).map((key: any) => {
      const paramsKey = filterKeys[key]
      if (queryParams.get(key)) {
        if (key !== 'customerId') {
          chips[key] = queryParams.get(key)
        }
      }
      if (queryParams.get(paramsKey)) {
        params[paramsKey] = queryParams.get(paramsKey)
      }
    })
  }
  return {
    chips,
    params,
  }
}

function removeQueryFilter(
  appliedFilters: any,
  chipKey: string,
  paramsKey: any
) {
  if (appliedFilters?.chips?.[chipKey]) {
    delete appliedFilters.chips[chipKey]
  }
  if (appliedFilters?.params?.[paramsKey]) {
    delete appliedFilters.params[paramsKey]
  }
  return appliedFilters
}

function getSearchDateFilter(appliedFilters: any) {
  const filters: any = {}
  if (appliedFilters && appliedFilters.fromDate) {
    filters.fromDate = appliedFilters.fromDate
  }
  if (appliedFilters && appliedFilters.toDate) {
    filters.toDate = appliedFilters.toDate
  }
  if (appliedFilters && appliedFilters.fromDatetime) {
    filters.fromDate = appliedFilters.fromDatetime
  }
  if (appliedFilters && appliedFilters.toDatetime) {
    filters.toDate = appliedFilters.toDatetime
  }
  return filters
}

function removeSearchDateFilter(appliedFilters: any) {
  const filters: any = Object.assign({}, appliedFilters)
  if (filters && filters.fromDate) {
    delete filters.fromDate
  }
  if (filters && filters.toDate) {
    delete filters.toDate
  }
  return filters
}

function getAdvanceFilterChips(filterChips: any) {
  let chips: any = Object.assign({}, filterChips)
  if (chips && chips.queryField) {
    delete chips['queryField']
  }
  if (chips && chips.queryFieldLabel) {
    delete chips['queryFieldLabel']
  }
  if (chips && chips.query && !isMobile) {
    delete chips['query']
  }
  if (
    chips &&
    chips.periodLabel &&
    chips.periodLabel === DispatchPeriodsEnum.Custom
  ) {
    chips = {
      ...chips,
      periodLabel: chips.fromDateLabel + ' to ' + chips.toDateLabel,
      fromDateLabel: '',
      toDateLabel: '',
    }
  }
  return chips
}

function createObjectQueryParams(obj: any) {
  return new URLSearchParams(obj).toString()
}

export {
  useQuery,
  createQueryParams,
  getFilterChipsAndParams,
  removeQueryFilter,
  getSearchDateFilter,
  removeSearchDateFilter,
  getAdvanceFilterChips,
  createObjectQueryParams,
}
