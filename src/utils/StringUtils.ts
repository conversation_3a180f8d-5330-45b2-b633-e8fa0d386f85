export function nullCheck(value: string, defaultValue: string) {
  return value === null || value === undefined ? defaultValue : value
}

export function isEmptyArray(value: Array<unknown> | null | undefined) {
  return value === null || value === undefined || value.length <= 0
}

export function isNullValue(value: unknown | null | undefined) {
  return (
    value === null ||
    value === undefined ||
    (typeof value === 'string' ? value.trim() : value) === ''
  )
}

export function isFilterNullValue(value: string | null | undefined) {
  return value === null || value === undefined || value === ''
}

export function isNullValueOrZero(value: string | null | undefined | number) {
  return value === null || value === undefined || value === '' || value === 0
}

export function convertToFloat(value: string) {
  return value === null || value === undefined || value === ''
    ? 0
    : Number.parseFloat(value)
}
export function isObjectEmpty(obj: Record<string, unknown> | null | undefined) {
  return (
    obj === null ||
    obj === undefined ||
    (Object.keys(obj).length === 0 && obj.constructor === Object)
  )
}

export function isObjectValueEmpty(
  obj: Record<string, unknown> | null | undefined
) {
  return (
    obj === null ||
    obj === undefined ||
    (Object.keys(obj).length === 0 && obj.constructor === Object) ||
    Object.keys(obj).some((keyName: string) => isNullValue(obj?.[keyName]))
  )
}

export function isValidEmailId(email: string | null | undefined): boolean {
  if (!email) return false

  const re =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}(\.[0-9]{1,3}){3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

  return re.test(email)
}

export function isValidMobileNumber(string: string) {
  const re = /^[6-9]{1}[0-9]{9}$/
  return re.test(string)
}

export function isValidPassword(string: string) {
  const re = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/
  return re.test(string)
}

export function vehicleNumberRegex(string: string) {
  const replaced_text = string.replace(/[^a-zA-Z0-9-]/g, '')
  return replaced_text.toString().toUpperCase()
}

export function hastDuplicateValue(array: Array<unknown>) {
  return array.some(function (value: unknown) {
    // .some will break as soon as duplicate found (no need to itterate over all array)
    return array.indexOf(value) !== array.lastIndexOf(value) // comparing first and last indexes of the same value
  })
}

export function commaSeparatedNumbers(number: number) {
  if (number) {
    const str = number.toString()
    const numArray = str.split('.')
    let a = []
    a = numArray
    const x = a[0]
    let lastThree = x.substring(x.length - 3)
    const otherNumbers = x.substring(0, x.length - 3)
    if (otherNumbers !== '') {
      lastThree = ',' + lastThree
    }
    const res =
      otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') +
      lastThree +
      '.' +
      ((a[1] === '0' ? '00' : a[1]) || '00')
    return res
  } else {
    return '0.00'
  }
}

export function tatValueConverter(unit: 'hr' | 'd', number: number) {
  if (unit === 'hr') {
    return number
  } else if (unit === 'd') {
    return 24 * number
  }
}

export function convertWordToTitleCase(str: string) {
  return str.charAt(0).toUpperCase() + str.substring(1).toLowerCase()
}
