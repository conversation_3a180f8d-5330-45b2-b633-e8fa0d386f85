import DayjsUtils from '@date-io/dayjs'
import dayjs, { Dayjs } from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

export const displayDateFormatter = 'DD/MM/YYYY'
export const DashboardDateFormatter = 'YYYY/MM/DD'
export const trackingDisplayDateFormatter = 'DD MMM, hh:mmA'
export const trackingDashboardDisplayDateFormatter = 'DD/MM/YYYY, hh:mmA'
export const displayDateTimeFormatter = 'DD/MM/YYYY hh:mm A'
export const display24DateTimeFormatter = 'YYYY-MM-DD HH:MM'
export const display24TimeFormatter = 'DD-MM-YYYY HH:MM'
export const displayTimeFormatter = 'hh:mm A'
export const displayTimeCounterFormatter = 'hh:mm'
export const serverDateFormat = 'YYYY-MM-DD'
export const hyphenDate = 'YYYY-MM-DD'

export function convertDateTimeServerFormat(date: Dayjs | string): string {
  try {
    return dayjs(date).utc().toISOString()
  } catch {
    return typeof date === 'string' ? date : (date?.toISOString() ?? '')
  }
}

export function convertDateServerFormat(date: Dayjs | string): string {
  try {
    return new DayjsUtils().date(date)?.format(serverDateFormat) ?? ''
  } catch {
    return typeof date === 'string' ? date : (date?.toISOString() ?? '')
  }
}

export function getMonthName(month: number | any): string {
  try {
    return dayjs().month(month).format('MMM')
  } catch {
    return 'NA'
  }
}

export function convertDateToServerFromDate(date: any): string {
  try {
    return dayjs(date).startOf('day').toISOString()
  } catch {
    return date?.toISOString?.() ?? ''
  }
}

export function convertDateToServerToDate(date: any): string {
  try {
    return dayjs(date).endOf('day').toISOString()
  } catch {
    return date?.toISOString?.() ?? ''
  }
}

export function convertDateFormat(date: any, format: string): string {
  try {
    return dayjs(date).format(format)
  } catch {
    return date?.toString?.() ?? ''
  }
}

export function getFutureDate(date: any, noOfDays: number): any {
  try {
    return dayjs(date).add(noOfDays, 'day')
  } catch {
    return date
  }
}

export function getPastDate(date: any, noOfDays: number, unit: any) {
  try {
    return dayjs(date).subtract(noOfDays, unit)
  } catch {
    return date
  }
}

export function previousMonth(date: any) {
  try {
    return dayjs(date).subtract(1, 'month')
  } catch {
    return date
  }
}

export function isDateGreater(fromDate: any, toDate: any) {
  try {
    return dayjs(fromDate).isAfter(dayjs(toDate))
  } catch {
    return false
  }
}

export function getDifferenceInDates(fromDate: any, toDate: any) {
  try {
    return dayjs(fromDate).diff(dayjs(toDate), 'hour')
  } catch {
    return 0
  }
}

export function getDifferenceInDatesAsMinutes(fromDate: any, toDate: any) {
  try {
    return dayjs(fromDate).diff(dayjs(toDate), 'minute', true)
  } catch {
    return 0
  }
}

export function getDifferenceInDatesAsDays(fromDate: any, toDate: any) {
  try {
    return dayjs(fromDate).diff(dayjs(toDate), 'day')
  } catch {
    return 0
  }
}

export function getDifferenceInDatesAs(fromDate: any, toDate: any, unit: any) {
  try {
    return dayjs(fromDate).diff(dayjs(toDate), unit)
  } catch {
    return 0
  }
}

export function getPastMonth(date: any, month: number) {
  try {
    return dayjs(date).subtract(month, 'month')
  } catch {
    return date
  }
}

export function getFutureMonth(date: any, month: number) {
  try {
    return dayjs(date).add(month, 'month')
  } catch {
    return date
  }
}

export function convertSecondsInHours(secs: any, showSecond?: boolean) {
  const totalSec = Number(secs)
  const h = Math.floor(totalSec / 3600)
  const m = Math.floor((totalSec % 3600) / 60)
  const s = Math.floor(totalSec % 60)

  const hDisplay = h > 0 ? `${h}${h === 1 ? ' hr ' : ' hrs '}` : ''
  const mDisplay = m > 0 ? `${m}${m === 1 ? ' min ' : ' mins '}` : ''
  const sDisplay = s > 0 ? `${s}${s === 1 ? ' sec ' : ' secs'}` : ''
  return showSecond ? hDisplay + mDisplay + sDisplay : hDisplay + mDisplay
}

export function convertMinutesInHours(mins: any) {
  const totalMin = Number(mins)
  const h = Math.floor(totalMin / 60)
  const m = totalMin % 60

  const hDisplay = h > 0 ? `${h}${h === 1 ? ' hr ' : ' hrs '}` : ''
  const mDisplay = m > 0 ? `${m}${m === 1 ? ' min ' : ' mins '}` : ''

  return hDisplay + mDisplay
}

export function convertHoursInDays(hours: any) {
  if (hours) {
    const totalHours = Number(hours)
    const d = Math.floor(totalHours / 24)
    const h = totalHours % 24
    return d && h ? `${d}d ${h}h` : d ? `${d}d` : h ? `${h}h` : 'NA'
  }
  return 'NA'
}

export function convertMinutesInDays(minutes: any) {
  if (!minutes) return 'NA'

  const totalMinutes = Number(minutes)
  const d = Math.floor(totalMinutes / (24 * 60))
  const h = Math.floor((totalMinutes % (24 * 60)) / 60)
  const m = totalMinutes % 60

  if (d && !h && !m) return `${d}d`
  if (d && h && m) return `${d}d ${h}h ${m}m`
  if (d && h && !m) return `${d}d ${h}h`
  if (!d && h && m) return `${h}h ${m}m`
  if (!d && !h && m) return `${m}m`
  if (!d && h && !m) return `${h}h`
  return `${d}d ${h}h ${m}m`
}

export const isDateUndefinedOrNull = (date: any) => !date
