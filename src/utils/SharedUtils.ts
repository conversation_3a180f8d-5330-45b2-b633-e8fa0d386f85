import { Location, LocationRecord } from '@/api/types'
import { OptionType } from '@/components/widgets/widgetsInterfaces'

export const createFormattedOriginsOptions = (
  data: Location[]
): OptionType[] => {
  const map = new Map<
    string,
    {
      origin: LocationRecord
      destinations: OptionType[]
      seenDestCodes: Set<string>
    }
  >()

  data.forEach(({ originData, destinationData }) => {
    const oCode = originData.locationCode
    const dCode = destinationData.locationCode

    if (!map.has(oCode)) {
      map.set(oCode, {
        origin: originData,
        destinations: [],
        seenDestCodes: new Set(),
      })
    }

    const entry = map.get(oCode)!
    // Only add destination if not already seen
    if (!entry.seenDestCodes.has(dCode)) {
      entry.destinations.push({
        label: destinationData.locationName,
        value: destinationData.locationCode,
        data: destinationData,
      })
      entry.seenDestCodes.add(dCode)
    }
  })

  const formattedOrigins = Array.from(map.values()).map(
    ({ origin, destinations }) => ({
      label: origin.locationName,
      value: origin.locationCode,
      data: { ...origin, destinations },
    })
  )

  return formattedOrigins
}
