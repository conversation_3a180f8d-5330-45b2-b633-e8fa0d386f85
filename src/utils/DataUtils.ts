import { OptionType } from '@/components/widgets/widgetsInterfaces'
import { isNullValue } from '@/utils/StringUtils'

export function setAutoCompleteListWithoutLabelAndValue(
  params: Array<any> | undefined
) {
  // use element  as label and value when array type is string
  return (
    (params &&
      params.map((element: any) => ({
        label: element,
        value: element,
      }))) ||
    []
  )
}

export function setAutoCompleteList(
  params: Array<any> | undefined,
  labelKey?: string,
  valueKey?: string
): Array<OptionType> {
  // use element  as label and value when array type is string
  return (
    (params &&
      params.map((element: any) => ({
        label: (labelKey && element[labelKey]) || 'label',
        value: (valueKey && element[valueKey]) || 'value',
      }))) ||
    []
  )
}

export function setAutoCompleteListFromObject(
  params: Array<any> | undefined,
  parentKey: string,
  labelKey?: string,
  valueKey?: string
): Array<OptionType> {
  // use element  as label and value when array type is string
  return (
    (params &&
      params.map((element: any) => ({
        label: (labelKey && element[parentKey][labelKey]) || 'label',
        value: (valueKey && element[parentKey][valueKey]) || 'value',
        data: element,
      }))) ||
    []
  )
}

export function setAutoCompleteListWithData(
  params: Array<any> | undefined,
  labelKey?: string,
  valueKey?: string
): Array<OptionType> {
  // use element  as label and value when array type is string
  return (
    (params &&
      params.map((element: any) => ({
        label: (labelKey && element[labelKey]) || 'label',
        value: (valueKey && element[valueKey]) || 'value',
        data: element,
      }))) ||
    []
  )
}

export function setAutoCompleteListCustomLabel(
  params: Array<any> | undefined,
  labelKey: string,
  valueKey: string,
  helperKey: string
): Array<OptionType> {
  // use element  as label and value when array type is string
  return (
    (params &&
      params.map((element: any) => ({
        label:
          (labelKey && element[labelKey]) +
            (helperKey && element[helperKey]
              ? ' ( ' + element[helperKey] + ' )'
              : '') || 'label',
        value: (valueKey && element[valueKey]) || 'value',
        data: element,
      }))) ||
    []
  )
}

export function getStringAutoCompleteData(
  params: Array<any> | null
): Array<any> {
  return (
    (params &&
      params.map((element: any) => ({
        label: element,
        value: element,
      }))) ||
    []
  )
}

export function createLocationObject(params: any) {
  return {
    code: params.code,
    lat: params.latitude,
    long: params.longitude,
    name: params.name,
  }
}

export function removeListItem(list: any, selected: any) {
  return (
    list &&
    list
      .filter((element: any) => element.index !== selected.index)
      .map((element: any, index: number) => ({
        ...element,
        index: index,
      }))
  )
}

export function groupBy(array: any, key: any) {
  // Return the end result
  return array.reduce((result: any, currentValue: any) => {
    // If an array already present for key, push it to the array. Else create an array and push the object
    ;(result[currentValue[key]] = result[currentValue[key]] || []).push(
      currentValue
    )
    // Return the current iteration `result` value, this will be taken as next iteration `result` value and accumulate
    return result
  }, {}) // empty object is the initial value for result object
}

export const round_decimals_up = (n: number, decimals: number = 2): number => {
  if (typeof n === 'string') {
    n = parseFloat(n)
  }

  // // Check if the digit after the desired decimal place is exactly 5
  if ((n * Math.pow(10, decimals + 1)) % 10 === 5) {
    // If it's 5, round up
    return Math.ceil(n * Math.pow(10, decimals)) / Math.pow(10, decimals)
  } else {
    // Otherwise, use standard rounding
    return parseFloat(n.toFixed(decimals))
  }
}

export function setAutoCompleteListWithValueLabelMapper(
  params: Array<any> | undefined,
  ValueLabelMapper: any
): Array<OptionType> {
  const labelValueList =
    ValueLabelMapper &&
    params &&
    params.map((element: any) => {
      const label = ValueLabelMapper[element as keyof typeof ValueLabelMapper]
      if (!isNullValue(label)) {
        return {
          label: ValueLabelMapper[element as keyof typeof ValueLabelMapper],
          value: element,
        }
      }
    })
  return labelValueList ? labelValueList.filter(Boolean) : []
}

export function setAutoCompleteListWithLabelConvertorAndValue(
  params: Array<any> | undefined,
  valueLabelConvertor: (value: any) => string,
  valueKey?: string
): Array<OptionType> {
  return (
    (params &&
      params.map((element: any, _index: number) => ({
        label: valueLabelConvertor(element),
        value: (valueKey && element[valueKey]) || 'value',
        data: element,
      }))) ||
    []
  )
}
