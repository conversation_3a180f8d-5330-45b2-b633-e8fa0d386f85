import { lazy, Suspense } from 'react'
import * as Path from '@/constant/RoutePath'
import {
  Navigate,
  Route,
  BrowserRouter as Router,
  Routes,
} from 'react-router-dom'
import Header from '@/components/header/Header'
import MessageAlertBox from '@/components/modals/AlertBox/MessageAlertBox'
import Loader from './components/loader/Loader'
import DataNotFound from './components/error/DataNotFound'

const Home = lazy(() => import('@/pages/loads/listing/components/Home'))
const CreateLoad = lazy(() => import('@/pages/createLoad/CreateLoad'))

function AppContainer() {
  return (
    <Router>
      <div id="wrapper" className="wrapper">
        <Header />
        <Suspense fallback={<Loader loading />}>
          <MessageAlertBox />
          <div className="main-page">
            <Routes>
              <Route path={Path.createLoad} element={<CreateLoad />} />
              <Route
                path={Path.home}
                element={<Navigate to="/pending" replace />}
              />
              <Route path="/:status" element={<Home />}></Route>
              <Route path="*" element={<DataNotFound />} />
            </Routes>
          </div>
        </Suspense>
      </div>
    </Router>
  )
}
export default AppContainer
