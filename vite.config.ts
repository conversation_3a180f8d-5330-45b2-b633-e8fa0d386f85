import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'

const PORT = 3000

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  build: {
    outDir: 'build',
  },
  server: {
    port: PORT,
    open: true,
    proxy: {
      '/_svc': {
        target: 'https://api-external.gobolt.dev',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  preview: {
    port: PORT,
    host: true,
  },
  base: '/',
})
