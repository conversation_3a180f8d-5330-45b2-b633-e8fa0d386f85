{"name": "load-board", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "clean": "rm -rf node_modules .next out dist build", "re:start": "yarn clean && yarn install && yarn start", "re:build": "yarn clean && yarn install && yarn build"}, "engines": {"node": "20.x"}, "dependencies": {"@date-io/dayjs": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.0.0", "@mui/material": "^6.0.0", "@mui/x-date-pickers": "^7.28.3", "@reduxjs/toolkit": "^2.6.1", "@sentry/react": "^9.12.0", "axios": "^1.8.4", "bootstrap": "^5.3.5", "dayjs": "^1.11.13", "errorhandler": "^1.5.1", "express": "^4.18.2", "http-proxy-middleware": "^3.0.5", "lodash": "^4.17.21", "react": "^19.0.0", "react-autosuggest": "^10.1.0", "react-dom": "^19.0.0", "react-number-format": "^4.4.1", "react-redux": "^9.2.0", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^7.5.0", "react-select": "^5.10.1", "redux-saga": "^1.3.0", "reduxsauce": "^2.0.0", "sass": "^1.86.3"}, "peerDependencies": {"@mui/material": "^5.15.14 || ^6.0.0 || ^7.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/errorhandler": "^1.5.3", "@types/eslint-config-prettier": "^6.11.3", "@types/lodash": "^4.17.16", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-autosuggest": "^10.1.11", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4"}}